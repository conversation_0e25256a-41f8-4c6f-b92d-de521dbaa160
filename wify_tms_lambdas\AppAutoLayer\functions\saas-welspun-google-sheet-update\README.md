# Welspun Service Requests to Google Spreadsheet Lambda

This Lambda function queries Welspun service requests data from a PostgreSQL database and updates a Google Spreadsheet with the results.

## Features

- **Modular Architecture**: Separated into distinct service modules for database operations and Google Sheets integration
- **Comprehensive Data Extraction**: Retrieves detailed service request information including customer details, task status, timestamps, and more
- **Chunked Updates**: Handles large datasets by processing data in chunks to avoid API rate limits
- **Error Handling**: Comprehensive error handling and logging throughout the process
- **Configurable**: Supports custom sheet names, ranges, and clearing options via event parameters

## Architecture

### Files Structure
```
saas-welspun-google-sheet-update/
├── index.js                 # Main Lambda handler
├── databaseService.js       # PostgreSQL database operations
├── googleSheetsService.js   # Google Sheets API operations
├── package.json            # Dependencies and metadata
├── creds.json              # Google Service Account credentials
└── README.md               # This documentation
```

### Modules

#### DatabaseService (`databaseService.js`)
- Handles PostgreSQL database connections
- Contains the complex SQL query for Welspun service requests
- Manages database connection lifecycle

#### GoogleSheetsService (`googleSheetsService.js`)
- Manages Google Sheets API authentication
- Handles spreadsheet updates with chunking for large datasets
- Provides utilities for clearing spreadsheet ranges

#### Main Handler (`index.js`)
- Orchestrates the entire process
- Handles Lambda event processing
- Manages error responses and logging

## Environment Variables

### Database Configuration
```
DB_HOST=your-database-host
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_PORT=5432
```

### Google Sheets Configuration
```
SPREADSHEET_ID=your-google-spreadsheet-id
```

## Event Parameters

The Lambda function accepts the following optional event parameters:

```json
{
  "sheetName": "Welspun_Service_Requests",
  "rangeStart": "Sheet1!A1",
  "clearSheet": true
}
```

- `sheetName`: Name of the sheet to update (default: "Welspun_Service_Requests")
- `rangeStart`: Starting cell range for data insertion (default: "SheetName!A1")
- `clearSheet`: Whether to clear existing data before updating (default: true)

## Data Schema

The function extracts the following fields from the database:

### Customer Information
- C_DATE, C_TIME: Creation date and time
- Service type, Ticket ID
- Mobile(+91), Name
- City, State, Zone

### Service Details
- OTP verification status
- Requested service date
- Location Group
- Feedback received status and comments
- Ratings

### Task Information
- Assignee name, Task status
- Assigned date and time
- Area/Zonal Service Manager

### Status Tracking
- New Case Date/Time
- Assigned Transaction Date/Time
- Scheduled Date/Time
- WIP Date/Time
- Issue Date/Time
- Completed Date/Time
- Replacement Date/Time
- Current Status and transition details

### Metrics
- TAT for Completed/Replacement Calls
- Case SO Number
- FTC (First Time Completion) details
- Total Area Sqft

## Usage

### Local Testing
```bash
npm install
node index.js
```

### Lambda Deployment
1. Update `creds.json` with your Google Service Account credentials
2. Set environment variables in Lambda configuration
3. Deploy the function with all files
4. Configure triggers as needed (CloudWatch Events, API Gateway, etc.)

### Manual Invocation
```bash
aws lambda invoke \
  --function-name saas-welspun-google-sheet-update \
  --payload '{"sheetName": "Custom_Sheet", "clearSheet": false}' \
  response.json
```

## Error Handling

The function includes comprehensive error handling:
- Database connection errors
- SQL query execution errors
- Google Sheets API errors
- Authentication failures
- Rate limiting handling

All errors are logged with detailed information and returned in the Lambda response.

## Performance Considerations

- **Chunked Processing**: Large datasets (>1000 records) are processed in chunks of 1000 records
- **Rate Limiting**: 1-second delays between chunks to respect Google Sheets API limits
- **Connection Management**: Database connections are properly closed after use
- **Memory Optimization**: Data is processed in streams where possible

## Dependencies

- `pg`: PostgreSQL client for Node.js
- `googleapis`: Google APIs client library
- `dotenv`: Environment variable management

## Security Notes

- Store Google Service Account credentials securely
- Use environment variables for sensitive configuration
- Ensure database credentials are encrypted in transit
- Follow principle of least privilege for database and Google Sheets access

## Monitoring and Logging

The function provides detailed logging at each step:
- Database connection and query execution
- Google Sheets authentication and updates
- Record counts and processing times
- Error details with stack traces

Monitor CloudWatch logs for operational insights and troubleshooting.
