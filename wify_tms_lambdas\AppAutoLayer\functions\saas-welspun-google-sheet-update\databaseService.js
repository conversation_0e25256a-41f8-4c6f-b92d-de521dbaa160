const { Client } = require("pg");
require("dotenv").config();

/**
 * Query database for Welspun service requests data
 * @returns {Array} Array of service request records
 */
async function queryWelspunServiceRequests() {
  console.log("queryWelspunServiceRequests :: Starting database query");

  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT || 5432,
  });

  try {
    await client.connect();
    console.log("queryWelspunServiceRequests :: Connected to database");

    const query = getWelspunServiceRequestsQuery();
    const result = await client.query(query);

    console.log(
      `queryWelspunServiceRequests :: Retrieved ${result.rows.length} records`
    );

    return result.rows;
  } catch (error) {
    console.error("queryWelspunServiceRequests :: Database error:", error);
    throw error;
  } finally {
    await client.end();
    console.log("queryWelspunServiceRequests :: Database connection closed");
  }
}

/**
 * Get the complete SQL query for Welspun service requests
 * @returns {string} SQL query string
 */
function getWelspunServiceRequestsQuery() {
  return `
      SELECT TO_CHAR(DATE(((srvc_req.c_meta).time)::timestamp at time zone 'utc' at time zone 'Asia/kolkata'), 'YYYY-MM-DD') as "C_DATE",
             srvc_type.title as "Service type",
             srvc_req.display_code as "Ticket ID",
             srvc_req.form_data#>>'{cust_mobile}' as "Mobile(+91)",
             srvc_req.form_data#>>'{cust_full_name}' as "Name",
             array_to_string(array(
                 SELECT value->>'label'
                   FROM jsonb_array_elements(
                            (
                                SELECT value
                                  FROM jsonb_array_elements(((srvc_type.form_data#>>'{srvc_cust_fields_json}')::jsonb#>>'{translatedFields}')::jsonb)
                                 WHERE 'e5783d1d-a974-4665-bb4a-4ae3e9005894' = value->>'key'
                            )->'options'
                        )
                  WHERE srvc_req.form_data#>>'{e5783d1d-a974-4665-bb4a-4ae3e9005894}' = value->>'value'
                     OR (
                            jsonb_typeof(srvc_req.form_data->'e5783d1d-a974-4665-bb4a-4ae3e9005894') = 'array'
                        AND value->>'value' = ANY(array(SELECT value FROM jsonb_array_elements_text(srvc_req.form_data->'e5783d1d-a974-4665-bb4a-4ae3e9005894')))
                        )
             ), ',') as "Zone",
             srvc_req.form_data->>'configure_consumer_otp_verification' as "OTP",
             TO_CHAR(( srvc_req.form_data#>>'{request_req_date}')::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD' ) as "Req. Service Date",
             srvc_req.form_data#>>'{cust_city}' as "City",
             srvc_req.form_data#>>'{cust_state}' as "State",
             tms_hlpr_get_loc_grps_name(srvc_req.org_id, srvc_req, tms_hlpr_get_loc_grp_json_by_org(srvc_req.org_id) ) as "Location Group",
             CASE WHEN srvc_req.form_data->'feedback_data' is not null THEN 'Yes' ELSE 'No' END as "Feedback Received (Yes/No)",
             srvc_req.form_data->'feedback_data'->'form_data'->>'71111c26-0679-46ea-8b6c-25fc0110e8e2' as "Feedback comments",
             (srvc_req.form_data->'feedback_data'->'form_data'->>(srvc_req.form_data->'feedback_data'->>'rating_field_key'))::int as "Ratings",
             sbtsk.form_data->'sbtsk_assignee'->>'label' as "Assignee name",
             sbtsk_statuses.title as "Task status",
             TO_CHAR(((sbtsk.c_meta).time )::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD' ) as "Assigned Date Time",
             zonal_manager_name.name as "Area Service Manager",
             zonal_manager_name.name as "Zonal Service Manager",
             (
                 SELECT TO_CHAR(((trnstn_log.c_meta).time)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                   FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                  WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                    AND trnstn_log.status_key = 'open'
             ) as "New Case Date Time",
             (
                 SELECT TO_CHAR((trnstn_log.trnstn_date)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                   FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                  WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                    AND trnstn_log.status_key = '9R0Qk7Vy'
             ) as "Assigned Txn Date Time",
             TO_CHAR(( srvc_req.form_data->>'2d977a23-cbd2-49ed-abb1-d55ff6dd01c2')::timestamp at time zone 'utc' at time zone 'Asia/kolkata','YYYY-MM-DD') as "Rescheduled Service Date Time",
             (
                 SELECT TO_CHAR((trnstn_log.trnstn_date)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                   FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                  WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                    AND trnstn_log.status_key = 'iESQY2ZK'
             ) as "Scheduled Date Time",
             (
                 SELECT TO_CHAR((trnstn_log.trnstn_date)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                   FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                  WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                    AND trnstn_log.status_key = 'ZE2u4bcM'
             ) as "WIP Date Time",
             (
                 SELECT TO_CHAR((trnstn_log.trnstn_date)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                   FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                  WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                    AND trnstn_log.status_key = '11Q8JlTp'
             ) as "Issue Date Time",
             (
                 SELECT TO_CHAR((trnstn_log.trnstn_date)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                   FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                  WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                    AND trnstn_log.status_key = 'closed'
             ) as "Completed Date Time",
             (
                 SELECT TO_CHAR((trnstn_log.trnstn_date)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                   FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                  WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                    AND trnstn_log.status_key = '8MBDqaXQ'
             ) as "Replacement Date Time",
             tms_get_srvc_status_details(srvc_req.srvc_type_id,last_srvc_txn_log.status_key)->>'title' as "Status",
             (
                 CASE WHEN (DATE((last_srvc_txn_log.u_meta).time) IS NULL) THEN
                           TO_CHAR(((last_srvc_txn_log.c_meta).time)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                      ELSE
                           TO_CHAR(((last_srvc_txn_log.u_meta).time)::timestamp at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD')
                 END
             ) as "Status Transition Date Time",
             (
                 (
                     SELECT DATE(trnstn_log.trnstn_date)
                       FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                      WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                        AND trnstn_log.status_key = 'closed'
                 )
                 -
                 DATE(((sbtsk.c_meta).time )::timestamp at time zone 'utc' at time zone 'Asia/kolkata')
             ) as "TAT for Completed Calls",
             (
                 (
                     SELECT DATE(trnstn_log.trnstn_date)
                       FROM cl_tx_srvc_req_trnstn_log as trnstn_log
                      WHERE trnstn_log.srvc_req_id = srvc_req.db_id
                        AND trnstn_log.status_key = '8MBDqaXQ'
                 )
                 -
                 DATE(((sbtsk.c_meta).time )::timestamp at time zone 'utc' at time zone 'Asia/kolkata')
             ) as "TAT for Replacement Calls",
             srvc_req.form_data->>'d6ac7e75-e935-4222-ae1a-318eebee811f' as "Case SO Number",
             array_to_string(
                 array(
                     SELECT value->>'label'
                       FROM jsonb_array_elements(
                                (
                                    SELECT value
                                      FROM jsonb_array_elements(
                                               (
                                                   (srvc_type.form_data#>>'{srvc_cust_fields_json}')::jsonb#>>'{translatedFields}'
                                               )::jsonb
                                           )
                                     WHERE value->>'key' IN (
                                               'c15bf73b-796e-47d7-9bd2-df5365b3fcb4',
                                               '0aa0bbe6-204b-4df6-9023-812547b2cd9f',
                                               'b908520a-e61b-4574-a6af-a900f59b82f8',
                                               '1dc97c8f-6c72-4654-ae03-4592a9b24148',
                                               '4d6d327d-4838-452a-905f-ad05f7d79f2a',
                                               'd4a18410-9979-444a-9880-2fee988d164b'
                                           )
                                )->'options'
                            )
                      WHERE (
                                srvc_req.form_data#>>'{c15bf73b-796e-47d7-9bd2-df5365b3fcb4}' = value->>'value'
                             OR (jsonb_typeof(srvc_req.form_data->'c15bf73b-796e-47d7-9bd2-df5365b3fcb4') = 'array'
                                 AND value->>'value' = ANY(
                                         array(
                                             SELECT value
                                               FROM jsonb_array_elements_text(srvc_req.form_data->'c15bf73b-796e-47d7-9bd2-df5365b3fcb4')
                                         )
                                     ))
                            )
                         OR (
                                srvc_req.form_data#>>'{0aa0bbe6-204b-4df6-9023-812547b2cd9f}' = value->>'value'
                             OR (jsonb_typeof(srvc_req.form_data->'0aa0bbe6-204b-4df6-9023-812547b2cd9f') = 'array'
                                 AND value->>'value' = ANY(
                                         array(
                                             SELECT value
                                               FROM jsonb_array_elements_text(srvc_req.form_data->'0aa0bbe6-204b-4df6-9023-812547b2cd9f')
                                         )
                                     ))
                            )
                         OR (
                                srvc_req.form_data#>>'{b908520a-e61b-4574-a6af-a900f59b82f8}' = value->>'value'
                             OR (jsonb_typeof(srvc_req.form_data->'b908520a-e61b-4574-a6af-a900f59b82f8') = 'array'
                                 AND value->>'value' = ANY(
                                         array(
                                             SELECT value
                                               FROM jsonb_array_elements_text(srvc_req.form_data->'b908520a-e61b-4574-a6af-a900f59b82f8')
                                         )
                                     ))
                            )
                         OR (
                                srvc_req.form_data#>>'{1dc97c8f-6c72-4654-ae03-4592a9b24148}' = value->>'value'
                             OR (jsonb_typeof(srvc_req.form_data->'1dc97c8f-6c72-4654-ae03-4592a9b24148') = 'array'
                                 AND value->>'value' = ANY(
                                         array(
                                             SELECT value
                                               FROM jsonb_array_elements_text(srvc_req.form_data->'1dc97c8f-6c72-4654-ae03-4592a9b24148')
                                         )
                                     ))
                            )
                         OR (
                                srvc_req.form_data#>>'{4d6d327d-4838-452a-905f-ad05f7d79f2a}' = value->>'value'
                             OR (jsonb_typeof(srvc_req.form_data->'4d6d327d-4838-452a-905f-ad05f7d79f2a') = 'array'
                                 AND value->>'value' = ANY(
                                         array(
                                             SELECT value
                                               FROM jsonb_array_elements_text(srvc_req.form_data->'4d6d327d-4838-452a-905f-ad05f7d79f2a')
                                         )
                                     ))
                            )
                         OR (
                                srvc_req.form_data#>>'{d4a18410-9979-444a-9880-2fee988d164b}' = value->>'value'
                             OR (jsonb_typeof(srvc_req.form_data->'d4a18410-9979-444a-9880-2fee988d164b') = 'array'
                                 AND value->>'value' = ANY(
                                         array(
                                             SELECT value
                                               FROM jsonb_array_elements_text(srvc_req.form_data->'d4a18410-9979-444a-9880-2fee988d164b')
                                         )
                                     ))
                            )
                 ),
                 ',') as "FTC",
             COALESCE(
                 TO_CHAR(( srvc_req.form_data->>'f8145f05-899f-4f67-861c-246e1de47d52' )::timestamp at time zone 'utc' at time zone 'Asia/Kolkata', 'YYYY-MM-DD'),
                 TO_CHAR(( srvc_req.form_data->>'8e4d87eb-8191-4185-83c2-40bd57d41e2a' )::timestamp at time zone 'utc' at time zone 'Asia/Kolkata', 'YYYY-MM-DD'),
                 TO_CHAR(( srvc_req.form_data->>'84851254-89c8-4ccf-8c47-9b060e3a6d7f' )::timestamp at time zone 'utc' at time zone 'Asia/Kolkata', 'YYYY-MM-DD'),
                 TO_CHAR(( srvc_req.form_data->>'84485cf1-cf92-49ee-b52e-d8e02b5fd5d9' )::timestamp at time zone 'utc' at time zone 'Asia/Kolkata', 'YYYY-MM-DD'),
                 TO_CHAR(( srvc_req.form_data->>'11e2f43f-5beb-45e5-b6f4-8337d9192998' )::timestamp at time zone 'utc' at time zone 'Asia/Kolkata', 'YYYY-MM-DD'),
                 TO_CHAR(( srvc_req.form_data->>'36a21042-f870-4272-8536-d3487a66238d' )::timestamp at time zone 'utc' at time zone 'Asia/Kolkata', 'YYYY-MM-DD')
             ) as "FTC Date Time",
             sbtsk.form_data->'update_type_data'->'closed'->'5c112fd8-2831-4275-a01c-936e5f107d2a' as "Total Area Sqft"
        FROM cl_tx_sbtsk as sbtsk
       INNER JOIN cl_cf_sbtsk_statuses as sbtsk_statuses
               ON sbtsk_statuses.status_key = sbtsk.status
              AND sbtsk_statuses.sbtsk_type_id = sbtsk.sbtsk_type
       INNER JOIN cl_tx_users as technician_name
               ON technician_name.usr_id = any(sbtsk.assigned_to)
       INNER JOIN cl_tx_srvc_req as srvc_req
               ON srvc_req.db_id = sbtsk.srvc_req_id
       INNER JOIN cl_cf_service_types as srvc_type
               ON srvc_type.service_type_id = srvc_req.srvc_type_id
       INNER JOIN cl_tx_orgs as org
               ON srvc_req.org_id = org.org_id
       INNER JOIN cl_tx_srvc_req_trnstn_log as last_srvc_txn_log
               ON last_srvc_txn_log.db_id = (
                      SELECT latest_srvc_txn_log.db_id
                        FROM cl_tx_srvc_req_trnstn_log as latest_srvc_txn_log
                       WHERE latest_srvc_txn_log.srvc_req_id = srvc_req.db_id
                         AND latest_srvc_txn_log.status_key = srvc_req.status
                       ORDER BY (latest_srvc_txn_log.c_meta).time desc
                       LIMIT 1
                  )
        LEFT JOIN cl_tx_orgs as prvdr_name
               ON prvdr_name.org_id = srvc_req.srvc_prvdr
        LEFT JOIN cl_tx_users as zonal_manager_name
               ON zonal_manager_name.usr_id::text = (srvc_req.form_data->>'authority_614')
        LEFT JOIN cl_tx_users as area_manager_name
               ON area_manager_name.usr_id::text = (srvc_req.form_data->>'authority_613')
       WHERE org.org_id = 332 -- 332 org_id is Welspun Flooring
       ORDER BY Date(sbtsk.start_time) desc`;
}

module.exports = {
  queryWelspunServiceRequests,
  getWelspunServiceRequestsQuery,
};
