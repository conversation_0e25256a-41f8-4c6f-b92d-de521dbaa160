const { google } = require("googleapis");
const fs = require("fs");
require("dotenv").config();

// Constants
const SERVICE_ACCOUNT_FILE = "/tmp/credentials.json";
const SCOPES = ["https://www.googleapis.com/auth/spreadsheets"];

/**
 * Initialize Google Sheets API client
 * @returns {Object} Google Sheets API client
 */
async function initializeSheetsAPI() {
  const auth = new google.auth.GoogleAuth({
    keyFile: SERVICE_ACCOUNT_FILE,
    scopes: SCOPES,
    autoRetry: true,
    maxRetries: 5,
    retryDelayMultiplier: 2,
    totalTimeout: 60000,
  });

  return google.sheets({
    version: "v4",
    auth,
    axios: {
      timeout: 60000,
    },
  });
}

/**
 * Write credentials to temporary file
 * @param {Object} credentials - Service account credentials
 */
function writeCredentialsToFile(credentials) {
  console.log("writeCredentialsToFile :: Writing credentials to temp file");
  fs.writeFileSync(SERVICE_ACCOUNT_FILE, JSON.stringify(credentials));
}

/**
 * Update Google Spreadsheet with data
 * @param {Array} data - Array of records to update
 * @param {string} rangeStart - Starting range for the spreadsheet update
 * @returns {Promise<void>}
 */
async function updateSpreadsheet(data, rangeStart = "Sheet1!A1") {
  console.log("updateSpreadsheet :: Starting spreadsheet update");

  const spreadsheetId = process.env.SPREADSHEET_ID;
  if (!spreadsheetId) {
    throw new Error("SPREADSHEET_ID environment variable is required");
  }

  if (!data || data.length === 0) {
    console.log("updateSpreadsheet :: No data to update");
    return;
  }

  try {
    const sheets = await initializeSheetsAPI();

    // Extract column headers from the first record
    const headers = Object.keys(data[0]);

    // Prepare values array with headers and data
    const values = [
      headers, // Header row
      ...data.map((row) =>
        headers.map((header) => {
          const value = row[header];
          return value === null || value === undefined ? "" : String(value);
        })
      ),
    ];

    console.log(
      `updateSpreadsheet :: Updating ${values.length - 1} data rows with ${
        headers.length
      } columns`
    );

    const resource = {
      values,
    };

    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: rangeStart,
      valueInputOption: "USER_ENTERED",
      resource,
    });

    console.log("updateSpreadsheet :: Spreadsheet updated successfully");
  } catch (error) {
    console.error("updateSpreadsheet :: Error updating spreadsheet:", error);
    throw error;
  }
}

/**
 * Clear Google Spreadsheet range
 * @param {string} range - Range to clear
 * @returns {Promise<void>}
 */
async function clearSpreadsheetRange(range = "Sheet1!A:Z") {
  console.log(`clearSpreadsheetRange :: Clearing range ${range}`);

  const spreadsheetId = process.env.SPREADSHEET_ID;
  if (!spreadsheetId) {
    throw new Error("SPREADSHEET_ID environment variable is required");
  }

  try {
    const sheets = await initializeSheetsAPI();

    await sheets.spreadsheets.values.clear({
      spreadsheetId,
      range,
    });

    console.log("clearSpreadsheetRange :: Range cleared successfully");
  } catch (error) {
    console.error("clearSpreadsheetRange :: Error clearing range:", error);
    throw error;
  }
}

/**
 * Update spreadsheet with chunked data for large datasets
 * @param {Array} data - Array of records to update
 * @param {string} rangeStart - Starting range for the spreadsheet update
 * @param {number} chunkSize - Number of rows per chunk
 * @returns {Promise<void>}
 */
async function updateSpreadsheetInChunks(
  data,
  rangeStart = "Sheet1!A1",
  chunkSize = 900
) {
  console.log(
    `updateSpreadsheetInChunks :: Processing ${data.length} records in chunks of ${chunkSize}`
  );

  const spreadsheetId = process.env.SPREADSHEET_ID;
  if (!spreadsheetId) {
    throw new Error("SPREADSHEET_ID environment variable is required");
  }

  if (!data || data.length === 0) {
    console.log("updateSpreadsheetInChunks :: No data to update");
    return;
  }

  try {
    const sheets = await initializeSheetsAPI();
    const headers = Object.keys(data[0]);

    // Extract sheet name and column from rangeStart
    const [sheetName, cellRange] = rangeStart.split("!");
    const startColumn = cellRange.match(/[A-Z]+/)[0];

    // First, clear the sheet to ensure we have enough space
    console.log("updateSpreadsheetInChunks :: Clearing existing data");
    await clearSpreadsheetRange(`${sheetName}!A:Z`);

    // Add rows if needed (estimate: header + data rows + buffer)
    const totalRowsNeeded = data.length + 10; // +10 for buffer
    console.log(
      `updateSpreadsheetInChunks :: Ensuring sheet has ${totalRowsNeeded} rows`
    );

    try {
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        resource: {
          requests: [
            {
              appendDimension: {
                sheetId: 0, // Assuming first sheet
                dimension: "ROWS",
                length: totalRowsNeeded,
              },
            },
          ],
        },
      });
    } catch (dimensionError) {
      console.log(
        "updateSpreadsheetInChunks :: Sheet already has sufficient rows or error adding rows:",
        dimensionError.message
      );
    }

    // Update headers first
    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: `${sheetName}!${startColumn}1`,
      valueInputOption: "USER_ENTERED",
      resource: {
        values: [headers],
      },
    });

    // Process data in chunks
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      const startRow = i + 2; // +2 because we start after headers (row 1) and arrays are 0-indexed
      const chunkRange = `${sheetName}!${startColumn}${startRow}`;

      const chunkValues = chunk.map((row) =>
        headers.map((header) => {
          const value = row[header];
          return value === null || value === undefined ? "" : String(value);
        })
      );

      console.log(
        `updateSpreadsheetInChunks :: Updating chunk ${
          Math.floor(i / chunkSize) + 1
        } (rows ${startRow} to ${
          startRow + chunk.length - 1
        }) with range ${chunkRange}`
      );

      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: chunkRange,
        valueInputOption: "USER_ENTERED",
        resource: {
          values: chunkValues,
        },
      });

      console.log(
        `updateSpreadsheetInChunks :: Successfully updated chunk ${
          Math.floor(i / chunkSize) + 1
        }`
      );

      // Add delay between chunks to avoid rate limiting
      if (i + chunkSize < data.length) {
        await delay(1000); // 1 second delay
      }
    }

    console.log("updateSpreadsheetInChunks :: All chunks updated successfully");
  } catch (error) {
    console.error(
      "updateSpreadsheetInChunks :: Error updating spreadsheet:",
      error
    );
    throw error;
  }
}

/**
 * Utility function to add delay
 * @param {number} ms - Milliseconds to delay
 * @returns {Promise<void>}
 */
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

module.exports = {
  initializeSheetsAPI,
  writeCredentialsToFile,
  updateSpreadsheet,
  clearSpreadsheetRange,
  updateSpreadsheetInChunks,
  delay,
};
