const { queryWelspunServiceRequests } = require("./databaseService");
const {
  writeCredentialsToFile,
  updateSpreadsheet,
  clearSpreadsheetRange,
  updateSpreadsheetInChunks,
} = require("./googleSheetsService");
require("dotenv").config();

/**
 * Main Lambda handler function
 * @param {Object} event - Lambda event object
 * @returns {Object} Lambda response object
 */
exports.handler = async (event) => {
  console.log("handler :: Lambda function started");
  console.log("handler :: Event:", JSON.stringify(event, null, 2));

  try {
    // Load credentials from the creds.json file
    const credentials = require("./creds.json");
    writeCredentialsToFile(credentials);

    // Query database for Welspun service requests
    console.log("handler :: Querying database for Welspun service requests");
    const serviceRequestsData = await queryWelspunServiceRequests();

    if (!serviceRequestsData || serviceRequestsData.length === 0) {
      console.log("handler :: No service requests data found");
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "No data found to update",
          recordsProcessed: 0,
        }),
      };
    }

    // Determine the range and sheet name from event or use defaults
    const sheetName = event.sheetName || "Sheet1";
    const rangeStart = event.rangeStart || `${sheetName}!A1`;
    const clearSheet = event.clearSheet !== false; // Default to true unless explicitly set to false

    // Clear existing data if requested
    if (clearSheet) {
      console.log("handler :: Clearing existing spreadsheet data");
      await clearSpreadsheetRange(`${sheetName}!A:Z`);
    }

    // Update Google Spreadsheet
    console.log("handler :: Updating Google Spreadsheet");

    // Use chunked update for large datasets (>900 records to be safe with Google Sheets limits)
    if (serviceRequestsData.length > 900) {
      await updateSpreadsheetInChunks(serviceRequestsData, rangeStart, 900);
    } else {
      await updateSpreadsheet(serviceRequestsData, rangeStart);
    }

    console.log("handler :: Lambda function completed successfully");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Welspun service requests data updated successfully",
        recordsProcessed: serviceRequestsData.length,
        sheetName: sheetName,
        rangeStart: rangeStart,
      }),
    };
  } catch (error) {
    console.error("handler :: Lambda function error:", error);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error updating Welspun service requests data",
        error: error.message,
        stack: error.stack,
      }),
    };
  }
};
