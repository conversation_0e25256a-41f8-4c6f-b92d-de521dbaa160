{"name": "saas-welspun-google-sheet-update", "version": "1.0.0", "description": "Lambda function to query Welspun service requests from PostgreSQL and update Google Spreadsheet", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "keywords": ["lambda", "postgresql", "google-sheets", "welspun", "service-requests", "aws"], "author": "WIFY TMS Team", "license": "ISC", "dependencies": {"pg": "^8.11.3", "googleapis": "^126.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}