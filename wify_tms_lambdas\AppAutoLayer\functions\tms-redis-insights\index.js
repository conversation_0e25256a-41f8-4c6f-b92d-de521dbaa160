const Redis = require("ioredis");
require("dotenv").config();

// Initialize Redis connection
const createRedisClient = () => {
  return new Redis({
    host: process.env.REDIS_HOST || "your-redis-endpoint.amazonaws.com",
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
  });
};

/**
 * Extract pattern from Redis key
 * @param {string} key - Redis key
 * @returns {string} - Extracted pattern
 */
function extractPattern(key) {
  // Split the key at the first underscore and take the prefix part
  const pattern = key.split("_")[0];
  return pattern || key; // Return original key if no underscore found
}

/**
 * Get memory usage for a specific key
 * @param {Redis} redis - Redis client instance
 * @param {string} key - Redis key
 * @returns {number} - Memory usage in bytes
 */
async function getKeyMemoryUsage(redis, key) {
  try {
    const memory = await redis.memory("usage", key);
    return memory || 0;
  } catch (error) {
    // If MEMORY USAGE command is not available, return 0
    return 0;
  }
}

/**
 * Determine and count patterns in Redis keys with memory usage
 * @param {Redis} redis - Redis client instance
 * @returns {Object} - Object containing pattern counts, memory usage, and statistics
 */
async function determineAndCountPatterns(redis) {
  let cursor = "0";
  let patternsCount = {};
  let patternsMemory = {}; // Track memory usage per pattern
  let patternKeys = {}; // Store keys for each pattern for memory sampling
  let totalKeys = 0;
  let processedKeys = 0;
  const batchSize = 1000; // Process keys in batches
  const memorySampleSize = 50; // Sample size for memory calculation per pattern

  console.log("Starting Redis key pattern analysis with memory usage...");

  try {
    // First pass: collect all keys and group by patterns
    do {
      const [newCursor, foundKeys] = await redis.scan(
        cursor,
        "COUNT",
        batchSize
      );
      cursor = newCursor;
      totalKeys += foundKeys.length;

      // Process each key and extract its pattern
      foundKeys.forEach((key) => {
        const pattern = extractPattern(key);

        // Count patterns
        patternsCount[pattern] = patternsCount[pattern]
          ? patternsCount[pattern] + 1
          : 1;

        // Store keys for memory sampling (limit to sample size to avoid memory issues)
        if (!patternKeys[pattern]) {
          patternKeys[pattern] = [];
        }
        if (patternKeys[pattern].length < memorySampleSize) {
          patternKeys[pattern].push(key);
        }

        processedKeys++;
      });

      // Log progress for large datasets
      if (processedKeys % 10000 === 0) {
        console.log(`Processed ${processedKeys} keys so far...`);
      }
    } while (cursor !== "0");

    console.log(`Key collection complete. Processed ${totalKeys} total keys.`);
    console.log("Starting memory usage analysis for each pattern...");

    // Second pass: calculate memory usage for each pattern
    for (const [pattern, keys] of Object.entries(patternKeys)) {
      let totalMemoryForPattern = 0;
      let sampledKeys = 0;

      console.log(
        `Analyzing memory for pattern '${pattern}' (${keys.length} sample keys)...`
      );

      // Sample memory usage from available keys
      for (const key of keys) {
        try {
          const keyMemory = await getKeyMemoryUsage(redis, key);
          totalMemoryForPattern += keyMemory;
          sampledKeys++;
        } catch (error) {
          console.warn(`Could not get memory for key ${key}:`, error.message);
        }
      }

      if (sampledKeys > 0) {
        // Calculate average memory per key for this pattern
        const avgMemoryPerKey = totalMemoryForPattern / sampledKeys;

        // Estimate total memory for all keys of this pattern
        const totalKeysForPattern = patternsCount[pattern];
        const estimatedTotalMemory = avgMemoryPerKey * totalKeysForPattern;

        patternsMemory[pattern] = {
          totalBytes: Math.round(estimatedTotalMemory),
          totalGB: (estimatedTotalMemory / (1024 * 1024 * 1024)).toFixed(4),
          avgBytesPerKey: Math.round(avgMemoryPerKey),
          sampledKeys: sampledKeys,
          totalKeys: totalKeysForPattern,
        };
      } else {
        patternsMemory[pattern] = {
          totalBytes: 0,
          totalGB: "0.0000",
          avgBytesPerKey: 0,
          sampledKeys: 0,
          totalKeys: patternsCount[pattern],
        };
      }
    }

    console.log(`Memory analysis complete.`);

    // Sort patterns by count (descending)
    const sortedPatterns = Object.entries(patternsCount)
      .sort(([, a], [, b]) => b - a)
      .reduce((r, [k, v]) => ({ ...r, [k]: v }), {});

    // Calculate total estimated memory usage
    const totalEstimatedBytes = Object.values(patternsMemory).reduce(
      (sum, mem) => sum + mem.totalBytes,
      0
    );
    const totalEstimatedGB = (
      totalEstimatedBytes /
      (1024 * 1024 * 1024)
    ).toFixed(4);

    return {
      totalKeys,
      uniquePatterns: Object.keys(patternsCount).length,
      patterns: sortedPatterns,
      patternMemoryUsage: patternsMemory,
      totalEstimatedMemoryBytes: totalEstimatedBytes,
      totalEstimatedMemoryGB: totalEstimatedGB,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error during pattern analysis:", error);
    throw error;
  }
}

/**
 * Get Redis memory usage information
 * @param {Redis} redis - Redis client instance
 * @returns {Object} - Memory usage statistics
 */
async function getRedisMemoryInfo(redis) {
  try {
    const info = await redis.info("memory");
    const memoryStats = {};

    info.split("\r\n").forEach((line) => {
      if (line.includes(":")) {
        const [key, value] = line.split(":");
        // Capture all memory-related stats, not just used_memory
        if (
          key.includes("memory") ||
          key.includes("overhead") ||
          key.includes("fragmentation")
        ) {
          memoryStats[key] = value;
        }
      }
    });

    // Calculate additional metrics
    if (memoryStats.used_memory) {
      const usedMemoryBytes = parseInt(memoryStats.used_memory);
      memoryStats.used_memory_gb = (
        usedMemoryBytes /
        (1024 * 1024 * 1024)
      ).toFixed(4);
    }

    if (memoryStats.used_memory_rss) {
      const rssBytes = parseInt(memoryStats.used_memory_rss);
      memoryStats.used_memory_rss_gb = (
        rssBytes /
        (1024 * 1024 * 1024)
      ).toFixed(4);
    }

    if (memoryStats.used_memory_peak) {
      const peakBytes = parseInt(memoryStats.used_memory_peak);
      memoryStats.used_memory_peak_gb = (
        peakBytes /
        (1024 * 1024 * 1024)
      ).toFixed(4);
    }

    return memoryStats;
  } catch (error) {
    console.error("Error getting memory info:", error);
    return {};
  }
}

/**
 * Get Redis general information
 * @param {Redis} redis - Redis client instance
 * @returns {Object} - General Redis statistics
 */
async function getRedisGeneralInfo(redis) {
  try {
    const info = await redis.info("server");
    const serverStats = {};

    info.split("\r\n").forEach((line) => {
      if (line.includes(":")) {
        const [key, value] = line.split(":");
        if (
          ["redis_version", "uptime_in_seconds", "uptime_in_days"].includes(key)
        ) {
          serverStats[key] = value;
        }
      }
    });

    return serverStats;
  } catch (error) {
    console.error("Error getting server info:", error);
    return {};
  }
}

/**
 * Analyze memory discrepancy and provide insights
 * @param {Object} patternMemoryUsage - Pattern memory usage data
 * @param {Object} redisMemoryInfo - Redis memory info from INFO command
 * @returns {Object} - Memory analysis and discrepancy explanation
 */
function analyzeMemoryDiscrepancy(patternMemoryUsage, redisMemoryInfo) {
  const estimatedTotal = Object.values(patternMemoryUsage).reduce(
    (sum, pattern) => sum + pattern.totalBytes,
    0
  );

  const actualUsedMemory = parseInt(redisMemoryInfo.used_memory || 0);
  const actualRssMemory = parseInt(redisMemoryInfo.used_memory_rss || 0);

  const discrepancyBytes = actualUsedMemory - estimatedTotal;
  const discrepancyGB = (discrepancyBytes / (1024 * 1024 * 1024)).toFixed(4);
  const discrepancyPercentage =
    actualUsedMemory > 0
      ? ((discrepancyBytes / actualUsedMemory) * 100).toFixed(2)
      : 0;

  return {
    estimatedMemoryFromPatterns: {
      bytes: estimatedTotal,
      gb: (estimatedTotal / (1024 * 1024 * 1024)).toFixed(4),
    },
    actualRedisMemory: {
      used_memory_bytes: actualUsedMemory,
      used_memory_gb: (actualUsedMemory / (1024 * 1024 * 1024)).toFixed(4),
      rss_memory_bytes: actualRssMemory,
      rss_memory_gb: (actualRssMemory / (1024 * 1024 * 1024)).toFixed(4),
    },
    discrepancy: {
      bytes: discrepancyBytes,
      gb: discrepancyGB,
      percentage: discrepancyPercentage,
    },
    possibleReasons: [
      "Redis overhead (data structures, indexes, expiration tracking)",
      "Memory fragmentation",
      "Replication buffers",
      "Pub/Sub channels and client buffers",
      "Lua script cache",
      "Module memory usage",
      "MEMORY USAGE command may not account for all overhead",
      "Sampling methodology (only 50 keys per pattern sampled)",
    ],
    recommendations: [
      "Use MEMORY DOCTOR command for Redis memory analysis",
      "Check memory fragmentation ratio",
      "Monitor replication and client buffers",
      "Consider increasing sample size for more accurate estimates",
      "Use MEMORY STATS command for detailed breakdown",
    ],
  };
}

/**
 * Get sample keys for each pattern (for debugging purposes)
 * @param {Redis} redis - Redis client instance
 * @param {Object} patterns - Pattern counts object
 * @param {number} sampleSize - Number of sample keys per pattern
 * @returns {Object} - Object containing sample keys for each pattern
 */
async function getSampleKeys(redis, patterns, sampleSize = 3) {
  const samples = {};
  let cursor = "0";

  try {
    // Initialize sample arrays for each pattern
    Object.keys(patterns).forEach((pattern) => {
      samples[pattern] = [];
    });

    do {
      const [newCursor, foundKeys] = await redis.scan(cursor, "COUNT", 1000);
      cursor = newCursor;

      foundKeys.forEach((key) => {
        const pattern = extractPattern(key);
        if (samples[pattern] && samples[pattern].length < sampleSize) {
          samples[pattern].push(key);
        }
      });

      // Check if we have enough samples for all patterns
      const allPatternsSampled = Object.values(samples).every(
        (arr) => arr.length >= sampleSize
      );
      if (allPatternsSampled) break;
    } while (cursor !== "0");

    return samples;
  } catch (error) {
    console.error("Error getting sample keys:", error);
    return {};
  }
}

/**
 * Main Lambda handler function
 * @param {Object} event - Lambda event object
 * @returns {Object} - Response object with Redis insights
 */
exports.handler = async (event) => {
  const redis = createRedisClient();

  try {
    console.log("Connecting to Redis...");
    await redis.connect();
    console.log("Successfully connected to Redis");

    // Get pattern analysis
    const patternAnalysis = await determineAndCountPatterns(redis);

    // Get memory information
    const memoryInfo = await getRedisMemoryInfo(redis);

    // Get general server information
    const serverInfo = await getRedisGeneralInfo(redis);

    // Get sample keys for debugging (optional, can be disabled for performance)
    const includeSamples = event.includeSamples !== false; // Default to true
    let sampleKeys = {};
    if (includeSamples && patternAnalysis.uniquePatterns > 0) {
      sampleKeys = await getSampleKeys(redis, patternAnalysis.patterns, 3);
    }

    // Analyze memory discrepancy
    const memoryAnalysis = analyzeMemoryDiscrepancy(
      patternAnalysis.patternMemoryUsage,
      memoryInfo
    );

    const response = {
      statusCode: 200,
      body: {
        success: true,
        data: {
          patternAnalysis,
          memoryInfo,
          memoryAnalysis,
          serverInfo,
          sampleKeys: includeSamples ? sampleKeys : undefined,
          analysisTimestamp: new Date().toISOString(),
        },
      },
    };

    console.log("Redis insights analysis completed successfully");
    console.log("Pattern summary:", JSON.stringify(patternAnalysis, null, 2));

    return response;
  } catch (error) {
    console.error("Error in Redis insights analysis:", error);

    return {
      statusCode: 500,
      body: {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      },
    };
  } finally {
    // Always disconnect from Redis
    if (redis.status === "ready") {
      await redis.disconnect();
      console.log("Disconnected from Redis");
    }
  }
};

// Export functions for testing
module.exports = {
  handler: exports.handler,
  extractPattern,
  determineAndCountPatterns,
  getKeyMemoryUsage,
  getRedisMemoryInfo,
  getRedisGeneralInfo,
  analyzeMemoryDiscrepancy,
  getSampleKeys,
  createRedisClient,
};
