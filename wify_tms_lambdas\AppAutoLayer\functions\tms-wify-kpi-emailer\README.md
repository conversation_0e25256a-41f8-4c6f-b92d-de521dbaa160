# TMS Wify KPI Emailer

A Lambda function that queries vertical KPI data from the database and sends comprehensive reports via email using SMTP credentials from environment variables.

## Features

- Queries vertical-wise KPI metrics including orders, supply, and deployment statistics
- Sends **category-wise emails** to individual managers based on their assigned verticals
- Sends **consolidated company-wide email** with all verticals data to EMAIL_CC address
- Generates professional HTML email templates with data tables and summary analytics
- Sends emails using configurable SMTP settings
- Modular design with separate email service and template modules
- Comprehensive error handling and logging
- Includes contextual analysis and KPI summaries

## Environment Variables

### Database Configuration

```
DB_HOST=your-database-host
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_PORT=5432
```

### SMTP Configuration

```
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=465
EMAIL_AUTH_USER=<EMAIL>
EMAIL_AUTH_PASS=your-email-password
EMAIL_FROM=<EMAIL>
```

### Email Recipients

```
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL> (for category managers)
EMAIL_CC=<EMAIL>,<EMAIL> (REQUIRED for consolidated company-wide email)
EMAIL_BCC=<EMAIL>,<EMAIL> (optional)
```

**Note**: The consolidated company-wide email will only be sent if `EMAIL_CC` is configured. This email contains all verticals data and is sent to the EMAIL_CC address with empty CC field as specified.

## File Structure

```
tms-wify-kpi-emailer/
├── index.js                        # Main Lambda handler
├── categoryManagers.js             # Category manager configuration
├── managerEmailProcessor.js        # Category-wise email processing
├── consolidatedEmailProcessor.js   # Company-wide consolidated email processing
├── dataService.js                  # Database query functions
├── emailService.js                 # Email sending functionality
├── htmlTemplate.js                 # HTML template generation
├── csvUtils.js                     # CSV generation utilities
├── package.json                    # Dependencies
└── README.md                       # This file
```

## Email Types

### 1. Category-wise Emails

- Sent to individual category managers based on their assigned verticals
- Each manager receives data only for their specific verticals
- Includes CSV attachments with open orders for verticals that have open orders > 0
- Configured in `categoryManagers.js`

### 2. Consolidated Company-wide Email

- Sent to EMAIL_CC address with all verticals data
- Contains comprehensive overview of all business verticals
- Includes executive summary with company-wide metrics and insights
- No CC recipients (empty CC field as specified)
- No attachments (consolidated view only)
- Only sent if EMAIL_CC environment variable is configured

## KPI Metrics Included

The function queries and reports the following metrics for each vertical:

### Order Metrics

- **All time orders**: Total orders ever created for the vertical
- **Active orders**: Currently open orders
- **New orders**: Orders created in the last 24 hours
- **Orders assigned**: New orders that got assigned to technicians
- **Orders closed**: Orders completed in the last 24 hours

### Supply Metrics

- **Active supply**: Total active technicians in the vertical
- **New supply**: Technicians onboarded in the last 24 hours
- **Deactivated supply**: Inactive technicians
- **Supply deployed**: Technicians assigned tasks today
- **Supply started > 0 tasks**: Technicians who started at least one task
- **Supply closed > 0 tasks**: Technicians who completed at least one task

## Usage

### Local Testing

```bash
# Install dependencies
npm install

# Set environment variables in .env file
# Run the function (if you have a local test setup)
```

### Lambda Deployment

Deploy this function to AWS Lambda and configure the environment variables in the Lambda console.

## Modules

### emailService.js

- `sendEmail(emailData)` - Sends email with provided configuration
- `getEmailCredentials()` - Retrieves SMTP credentials from environment

### htmlTemplate.js

- `generateDataTableHTML(data, options)` - Creates HTML table from query results
- `generateEmptyDataHTML(title, description)` - Creates HTML for empty data scenarios
- `generateDataSummary(data)` - Creates contextual analysis summary
- `escapeHtml(text)` - Escapes HTML special characters
- `getISTTimestamp()` - Gets current timestamp in IST format

## Email Template Features

- **Responsive Design**: Works on desktop and mobile devices
- **Professional Styling**: Clean, modern appearance with gradient headers
- **Data Summary**: Contextual analysis with key insights and percentages
- **Interactive Tables**: Hover effects and alternating row colors
- **Comprehensive Metrics**: All KPI data in an easy-to-read format
- **Timestamp**: Generation time in IST timezone
- **Record Count**: Total number of verticals included in the report

## Error Handling

- Database connection errors are caught and logged
- Email sending failures are handled gracefully
- Missing environment variables are handled with defaults
- Empty data scenarios are handled with appropriate messaging

## Logging

All functions use the established logging pattern:

```
ClassName::functionName:: Log message
```

This helps with debugging and monitoring in production environments.
