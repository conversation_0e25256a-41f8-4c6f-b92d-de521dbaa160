const categoryManagers = require("./categoryManagers");
const { getVerticalKPIData } = require("./dataService");
const { processManagerEmails } = require("./managerEmailProcessor");
const { sendConsolidatedEmail } = require("./consolidatedEmailProcessor");
require("dotenv").config(); // Load environment variables

exports.handler = async () => {
  try {
    console.log(
      "TmsWifyKpiEmailer::handler:: Starting KPI email process for category managers and consolidated email"
    );

    // Get data from database
    const verticalKPIData = await getVerticalKPIData();
    console.log(
      "TmsWifyKpiEmailer::handler:: Retrieved data rows:",
      verticalKPIData.length
    );

    // Process and send emails to all category managers
    const results = await processManagerEmails(
      verticalKPIData,
      categoryManagers
    );

    console.log(
      `TmsWifyKpiEmailer::handler:: Category manager emails completed. Success: ${results.successCount}, Failed: ${results.failureCount}`
    );

    // Send consolidated company-wide email to EMAIL_CC
    const consolidatedResult = await sendConsolidatedEmail(verticalKPIData);

    console.log(
      `TmsWifyKpiEmailer::handler:: Consolidated email result: ${consolidatedResult.status}`
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Category manager KPI emails and consolidated email processed",
        categoryManagerResults: {
          totalManagers: results.emailResults.length,
          successCount: results.successCount,
          failureCount: results.failureCount,
          totalAttachmentsCount: results.totalAttachments.length,
          results: results.emailResults,
        },
        consolidatedEmailResult: consolidatedResult,
        recordsCount: verticalKPIData.length,
      }),
    };
  } catch (error) {
    console.error("TmsWifyKpiEmailer::handler:: Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal server error",
        error: error.message,
      }),
    };
  }
};
