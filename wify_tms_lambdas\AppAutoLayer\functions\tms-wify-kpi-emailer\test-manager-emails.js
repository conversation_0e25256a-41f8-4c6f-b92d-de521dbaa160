/**
 * Test script to validate that individual manager emails also use SQL aggregation
 * This script tests the managerEmailProcessor integration with getManagerTotals
 */

const { getManagerTotals } = require("./dataService");
const { generateDataTableHTML } = require("./htmlTemplate");
const categoryManagers = require("./categoryManagers");

// Mock database credentials for testing
process.env.DB_HOST = "localhost";
process.env.DB_NAME = "test_db";
process.env.DB_USER = "test_user";
process.env.DB_PASSWORD = "test_password";
process.env.DB_PORT = "5432";

async function testManagerEmailIntegration() {
  console.log("=== Testing Manager Email SQL Integration ===\n");

  try {
    // Test 1: Verify managerEmailProcessor imports getManagerTotals
    console.log("1. Testing managerEmailProcessor imports...");
    const managerEmailProcessor = require("./managerEmailProcessor");
    console.log("✅ managerEmailProcessor loaded successfully");

    // Test 2: Test HTML template with manager totals
    console.log("\n2. Testing HTML template with manager totals...");
    
    // Mock vertical data for a manager
    const mockVerticalData = [
      {
        "Vertical ID": 4,
        Title: "MKW Installation",
        "All time orders": 1250,
        "Open orders": 45,
        "New orders": 8,
        "Orders closed": 5,
        "Tech Inch.": 3,
        "Orders Sch.": 12,
        "Orders Sch. for yest. ": 10,
        "Tasks created": 15,
        "Supp. depl": 18,
        "Supply all task closed": 12,
        "Supply no task update": 2,
        "Supply partial task update": 4,
        "Active supply": 25,
        "New supply": 2,
        "Inactive supply": 5,
      },
      {
        "Vertical ID": 5,
        Title: "MKW Services",
        "All time orders": 890,
        "Open orders": 32,
        "New orders": 5,
        "Orders closed": 3,
        "Tech Inch.": 2,
        "Orders Sch.": 8,
        "Orders Sch. for yest. ": 7,
        "Tasks created": 10,
        "Supp. depl": 15,
        "Supply all task closed": 10,
        "Supply no task update": 1,
        "Supply partial task update": 4,
        "Active supply": 20,
        "New supply": 1,
        "Inactive supply": 3,
      }
    ];

    // Mock manager totals (what would come from SQL)
    const mockManagerTotals = {
      allTimeOrders: 2140,
      openOrders: 77,
      newOrders: 13,
      ordersClosed: 8,
      techInch: 5,
      ordersScheduled: 20,
      ordersSchYest: 17,
      tasksCreated: 25,
      supplyDeployed: 33,
      supplyAllTaskClosed: 22,
      supplyNoTaskUpdate: 3,
      supplyPartialTaskUpdate: 8,
      activeSupply: 45,
      newSupply: 3,
      inactiveSupply: 8,
      verticalCount: 2,
    };

    // Test HTML generation with manager totals
    const htmlContent = generateDataTableHTML(mockVerticalData, {
      title: "Test Manager KPI Report",
      description: "Testing SQL integration for manager emails",
      managerTotals: mockManagerTotals,
    });

    console.log("✅ HTML content generated with manager totals");
    console.log("HTML length:", htmlContent.length, "characters");
    
    // Check if the HTML contains indicators of SQL usage
    const containsSQLIndicator = htmlContent.includes("Using SQL-calculated totals");
    console.log("Contains SQL calculation indicator:", containsSQLIndicator ? "✅ Yes" : "⚠️  No (may be normal in production)");

    // Test 3: Test without manager totals (fallback)
    console.log("\n3. Testing HTML template without manager totals (fallback)...");
    const htmlContentFallback = generateDataTableHTML(mockVerticalData, {
      title: "Test Manager KPI Report - Fallback",
      description: "Testing JavaScript fallback for manager emails",
    });

    console.log("✅ HTML content generated with JavaScript fallback");
    console.log("Fallback HTML length:", htmlContentFallback.length, "characters");

    // Test 4: Verify the structure of manager email processing
    console.log("\n4. Testing manager email processing structure...");
    
    // Check if sendManagerEmail function exists and is properly structured
    const fs = require('fs');
    const managerEmailContent = fs.readFileSync('./managerEmailProcessor.js', 'utf8');
    
    const structureChecks = [
      { pattern: /getManagerTotals/, description: "Imports getManagerTotals function" },
      { pattern: /await getManagerTotals\(managerVerticalIds\)/, description: "Calls getManagerTotals with await" },
      { pattern: /managerTotals: managerTotals/, description: "Passes manager totals to HTML template" },
      { pattern: /console\.log.*vertical IDs/, description: "Logs vertical IDs for debugging" }
    ];
    
    for (const check of structureChecks) {
      if (check.pattern.test(managerEmailContent)) {
        console.log(`   ✅ ${check.description}`);
      } else {
        console.log(`   ❌ Missing: ${check.description}`);
      }
    }

    // Test 5: Verify HTML template structure
    console.log("\n5. Testing HTML template structure...");
    const htmlTemplateContent = fs.readFileSync('./htmlTemplate.js', 'utf8');
    
    const htmlChecks = [
      { pattern: /generateDataSummary.*preCalculatedTotals/, description: "generateDataSummary accepts preCalculatedTotals" },
      { pattern: /generateTotalsRow.*preCalculatedTotals/, description: "generateTotalsRow accepts preCalculatedTotals" },
      { pattern: /managerTotals = null/, description: "generateDataTableHTML accepts managerTotals option" },
      { pattern: /generateDataSummary\(data, managerTotals\)/, description: "Passes managerTotals to generateDataSummary" },
      { pattern: /generateTotalsRow\(data, headers, managerTotals\)/, description: "Passes managerTotals to generateTotalsRow" }
    ];
    
    for (const check of htmlChecks) {
      if (check.pattern.test(htmlTemplateContent)) {
        console.log(`   ✅ ${check.description}`);
      } else {
        console.log(`   ❌ Missing: ${check.description}`);
      }
    }

    console.log("\n" + "=".repeat(80));
    console.log("🎉 Manager Email SQL Integration validation completed!");
    
    console.log("\nSummary of changes for individual manager emails:");
    console.log("✅ Updated managerEmailProcessor.js to import getManagerTotals");
    console.log("✅ Modified sendManagerEmail to calculate totals using SQL");
    console.log("✅ Updated generateDataTableHTML to accept managerTotals option");
    console.log("✅ Enhanced generateDataSummary to use pre-calculated totals");
    console.log("✅ Enhanced generateTotalsRow to use pre-calculated totals");
    console.log("✅ Added fallback to JavaScript calculation when SQL totals not available");
    
    console.log("\nNow both consolidated AND individual manager emails use SQL aggregation!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testManagerEmailIntegration();
}

module.exports = {
  testManagerEmailIntegration
};
