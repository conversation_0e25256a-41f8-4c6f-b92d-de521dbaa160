{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:login": "playwright test tests/login.spec.js", "test:login:ui": "playwright test tests/login.spec.js --ui", "report": "playwright show-report", "install-browsers": "playwright install", "create-test": "npx tsx create_test.ts", "record": "playwright codegen http://localhost:3000"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.53.2", "@types/node": "^24.0.12", "tsx": "^4.7.0", "typescript": "^5.3.0"}}