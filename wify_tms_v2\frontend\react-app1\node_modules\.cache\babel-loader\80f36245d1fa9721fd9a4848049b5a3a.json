{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\containers\\\\SignIn.js\";\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Button, Checkbox, Form, Input, Tabs, Statistic, Modal, List, Avatar, message, Typography } from 'antd';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { MailOutlined, MobileOutlined, PhoneOutlined, SyncOutlined } from '@ant-design/icons';\nimport { superAdminSignIn, userSignIn } from '../appRedux/actions/Auth';\nimport IntlMessages from 'util/IntlMessages';\nimport InfoView from 'components/InfoView';\nimport FormBuilder from 'antd-form-builder';\nimport { number } from 'prop-types';\nimport { SELECT_ORG_FOR_LOGIN } from '../constants/ActionTypes';\nimport { isAndroidApp, isMobileView, setHtmlHeadTitle } from '../util/helpers';\nimport android_icon from '../assets/images/android_icon.png';\nimport android_play_icon from \"../assets/images/google_play_icon.svg\";\nimport { TypeAnimation } from 'react-type-animation';\nimport LaborDay from '../components/Aesthetics/LaborDay';\nimport { HiOutlineDownload } from 'react-icons/hi';\nconst {\n  Countdown\n} = Statistic;\nconst {\n  Text\n} = Typography;\nconst tms_apk_download_link = process.env.REACT_APP_TMS_APK_LINK_TO_DOWNLOAD;\nconst website_url = process.env.REACT_APP_WEBSITE_URL || 'https://home.wify.co.in';\nconst SignIn = props => {\n  var _props$location4, _props$location4$stat, _props$location4$stat2;\n  // const {render_helper} = useState()\n  const [timeOfDay, setTimeOfDay] = useState(getTimeOfDay());\n  const [screenWidth, setScreenWidth] = useState(window.innerWidth);\n  const [renderHelper, setRenderHelper] = useState(false);\n  const [superAdminAccessFlag, setsuperAdminAccessFlag] = useState(false);\n  const [ellipsis, setEllipsis] = useState(true);\n  const [filteredOrgArray, setFilteredOrgArray] = useState(null);\n  const dispatch = useDispatch();\n  const mobileLoginFormRef = useRef();\n  const token = useSelector(({\n    auth\n  }) => auth.token);\n  const otp_sent = useSelector(({\n    auth\n  }) => auth.otp_sent);\n  const otp_expiry = useSelector(({\n    auth\n  }) => auth.otp_expiry);\n  const is_multi_orgs = useSelector(({\n    auth\n  }) => auth.is_multi_orgs);\n  const multi_orgs_payload = useSelector(({\n    auth\n  }) => auth.multi_orgs_payload);\n  const orgsSearchInputRef = useRef(null);\n  useEffect(() => {\n    if (is_multi_orgs && (multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length) > 3) {\n      orgsSearchInputRef.current.focus();\n    }\n    setHtmlHeadTitle(props.location.pathname);\n  }, [is_multi_orgs]);\n\n  // console.log(\"multi_orgs_payload\",multi_orgs_payload);\n  const onFinishFailed = errorInfo => {\n    console.log('Failed:', errorInfo);\n  };\n  const onResendOTPClick = () => {\n    let form_data = mobileLoginFormRef.current.getFieldsValue();\n    form_data.id_key = undefined;\n    onFinish(form_data);\n  };\n  const handleCancel = () => {\n    let payload = {\n      ...multi_orgs_payload,\n      resp: undefined\n    };\n    setFilteredOrgArray(null);\n    dispatch({\n      type: SELECT_ORG_FOR_LOGIN,\n      payload: payload\n    });\n  };\n  const handleOrgSelect = org_id => {\n    if (multi_orgs_payload.form_data.id_type == 'MOBILE_NUM') {\n      mobileLoginFormRef.current.setFieldsValue({\n        org_id: org_id\n      });\n    }\n    // message.success('seelcted org ' + org_id);\n    let newFormData = {\n      ...multi_orgs_payload.form_data,\n      org_id: org_id\n    };\n    onFinish(newFormData);\n    dispatch({\n      type: SELECT_ORG_FOR_LOGIN,\n      payload: undefined\n    });\n  };\n  const onFinish = values => {\n    // dispatch({type:FETCH_START});\n    setFilteredOrgArray(null);\n    dispatch(userSignIn(values));\n  };\n  const onOTPCountDownFinish = () => {\n    setRenderHelper(!renderHelper);\n  };\n\n  // Function to determine the CSS class for the brand container based on the number of organizations.\n  const brandContainerByCount = orgListLength => {\n    // Check if the number of organizations is greater than 9.\n    if (orgListLength > 9) {\n      // If more than 9, apply custom scroll bar and list container styling.\n      return 'wy-scroll-bar-custom wy-brands-list-container';\n    }\n    // Check if the number of organizations is exactly 9.\n    else if (orgListLength == 9) {\n      // If exactly 9, apply a specific styling for the count of nine.\n      return 'wy-brands-list-count-nine';\n    }\n    // Check if the number of organizations is less than 9 but greater than 6.\n    else if (orgListLength < 9 && orgListLength > 6) {\n      // If between 7 and 9, apply a specific styling for counts from six to nine.\n      return 'wy-brands-list-count-six-to-nine';\n    }\n    // Check if the number of organizations is less than 6 but greater than 3.\n    else if (orgListLength < 6 && orgListLength > 3) {\n      // If between 4 and 6, apply a specific styling for counts from three to six.\n      return 'wy-brands-list-count-three-to-six';\n    }\n    // Check if the number of organizations is 3 or fewer.\n    else if (orgListLength <= 3) {\n      // If 3 or fewer, apply a specific styling for counts up to three.\n      return 'wy-brands-list-count-three';\n    }\n    // In case none of the above conditions are met, return an empty string.\n    else {\n      return '';\n    }\n  };\n  const getMobilNumFormMeta = () => {\n    const enter_otp_meta = [{\n      key: 'id_key',\n      type: 'number',\n      colSpan: 1,\n      placeholder: 'Enter OTP',\n      rules: [{\n        required: true,\n        pattern: new RegExp('^[0-9]*$'),\n        message: 'Enter your right OTP'\n      }, {\n        min: 4,\n        message: 'OTP must be min 4 digits.'\n      }, {\n        max: 4,\n        message: 'OTP must be max 4 digits.'\n      }]\n    }, {\n      key: 'retry',\n      colSpan: 1,\n      render() {\n        return /*#__PURE__*/React.createElement(React.Fragment, null, otp_expiry && otp_expiry > Math.floor(Date.now()) ? /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 33\n          }\n        }, \"Retry in\", ' ', /*#__PURE__*/React.createElement(Countdown, {\n          className: \"gx-d-inline-block\",\n          title: null,\n          format: \"ss\",\n          value: otp_expiry,\n          onFinish: onOTPCountDownFinish,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 37\n          }\n        }), \"s\") : /*#__PURE__*/React.createElement(Button, {\n          type: \"link\",\n          onClick: e => onResendOTPClick(),\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 33\n          }\n        }, /*#__PURE__*/React.createElement(SyncOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 37\n          }\n        }), \" Resend OTP\"));\n      }\n    }];\n    const meta = {\n      columns: 2,\n      formItemLayout: null,\n      fields: [{\n        key: 'id_type',\n        colSpan: 1,\n        initialValue: 'MOBILE_NUM',\n        className: 'gx-d-none'\n      }, {\n        key: 'org_id',\n        colSpan: 1,\n        className: 'gx-d-none'\n      }, {\n        key: 'id',\n        colSpan: 2,\n        type: 'number',\n        placeholder: 'Mobile no.',\n        rules: [{\n          required: true,\n          pattern: new RegExp('^[0-9]*$'),\n          message: 'Please enter your right mobile number!'\n        }, {\n          min: 10,\n          message: 'Mobile no must be min 10 digits.'\n        }, {\n          max: 10,\n          message: 'Mobile no must be max 10 digits.'\n        }]\n      }, ...(otp_sent ? enter_otp_meta : '')]\n    };\n    return meta;\n  };\n  useEffect(() => {\n    if (!superAdminAccessFlag) {\n      let superAdminParams = decodeParamsFrSuprAdminLogin();\n      if (superAdminParams) {\n        setsuperAdminAccessFlag(true);\n        dispatch(userSignIn(superAdminParams));\n        return;\n      }\n    }\n    if (token !== null) {\n      var _props$location, _props$location$state, _props$location$state2;\n      if (props === null || props === void 0 ? void 0 : (_props$location = props.location) === null || _props$location === void 0 ? void 0 : (_props$location$state = _props$location.state) === null || _props$location$state === void 0 ? void 0 : (_props$location$state2 = _props$location$state.from) === null || _props$location$state2 === void 0 ? void 0 : _props$location$state2.search) {\n        var _props$location2, _props$location2$stat, _props$location2$stat2, _props$location3, _props$location3$stat, _props$location3$stat2;\n        props.history.push(`${props === null || props === void 0 ? void 0 : (_props$location2 = props.location) === null || _props$location2 === void 0 ? void 0 : (_props$location2$stat = _props$location2.state) === null || _props$location2$stat === void 0 ? void 0 : (_props$location2$stat2 = _props$location2$stat.from) === null || _props$location2$stat2 === void 0 ? void 0 : _props$location2$stat2.pathname}${props === null || props === void 0 ? void 0 : (_props$location3 = props.location) === null || _props$location3 === void 0 ? void 0 : (_props$location3$stat = _props$location3.state) === null || _props$location3$stat === void 0 ? void 0 : (_props$location3$stat2 = _props$location3$stat.from) === null || _props$location3$stat2 === void 0 ? void 0 : _props$location3$stat2.search}`);\n      } else {\n        props.history.push('/');\n      }\n    }\n  }, [token, props.history, props === null || props === void 0 ? void 0 : (_props$location4 = props.location) === null || _props$location4 === void 0 ? void 0 : (_props$location4$stat = _props$location4.state) === null || _props$location4$stat === void 0 ? void 0 : (_props$location4$stat2 = _props$location4$stat.from) === null || _props$location4$stat2 === void 0 ? void 0 : _props$location4$stat2.search]);\n  const decodeParamsFrSuprAdminLogin = () => {\n    let searchParams = new URLSearchParams(window.location.search);\n    let user_id = searchParams.get('user_id');\n    let my_token = searchParams.get('my_token');\n    let is_check_technician_app = true;\n    if (user_id && my_token) {\n      return {\n        id_type: 'SUPER_ADMIN_ACCESS',\n        id: user_id,\n        id_key: my_token,\n        is_check_technician_app\n      };\n    }\n  };\n  function getTimeOfDay() {\n    const currentTime = new Date();\n    const hours = currentTime.getHours();\n    if (hours >= 5 && hours < 12) {\n      return 'morning';\n    } else if (hours >= 12 && hours < 16) {\n      return 'afternoon';\n    } else if (hours >= 16 && hours < 19) {\n      return 'evening';\n    } else {\n      return 'night';\n    }\n  }\n  useEffect(() => {\n    setTimeOfDay(getTimeOfDay());\n  }, []);\n\n  // Function to filter a list of organizations based on a search event's input.\n  const filterMultiOrgs = (searchEvent, multiOrgsData) => {\n    // Retrieve the search string entered by the user from the event target's value.\n    let searchString = searchEvent.target.value;\n\n    // Declare a variable to hold the filtered list of organizations.\n    let filteredOrgsList;\n\n    // Check if the search string has a length greater than 0, meaning the user has entered some text.\n    if (searchString.length > 0) {\n      // Filter the list of organizations by checking if the organization's name includes the search string.\n      // Convert both the organization's name and the search string to lowercase to make the search case-insensitive.\n      filteredOrgsList = multiOrgsData.filter(item => item.name.toLowerCase().includes(searchString.toLowerCase()));\n    }\n\n    // Update the state that stores the filtered list of organizations.\n    setFilteredOrgArray(filteredOrgsList);\n  };\n  const getGridViewCountData = () => {\n    return {\n      gutter: 16,\n      xs: 1,\n      sm: 2,\n      md: 3,\n      lg: (multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length) == 2 ? 2 : 3,\n      xl: (multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length) == 2 ? 2 : 3,\n      xxl: (multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length) == 2 ? 2 : 3\n    };\n  };\n  const isProduction = process.env.NODE_ENV == 'production';\n  const showPlayStoreIcon = process.env.REACT_APP_PLAYSTORE == 'true';\n  return !superAdminAccessFlag && /*#__PURE__*/React.createElement(\"div\", {\n    className: `gx-app-login-wrap ${timeOfDay} gx-overflow-hidden`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"animation-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"particle particle-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"particle particle-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"particle particle-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"particle particle-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 21\n    }\n  })), /*#__PURE__*/React.createElement(\"h3\", {\n    className: ` gx-mt-3 custom-text blend gx-z-index-20 ${timeOfDay == 'morning' ? 'gx-text-black' : 'gx-text-white'}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 17\n    }\n  }, \"Your\", ' ', /*#__PURE__*/React.createElement(TypeAnimation, {\n    sequence: ['Time', 3000, 'Task', 3000, 'Ticket', 3000, 'Team', 3000],\n    speed: 250,\n    repeat: Infinity,\n    className: \"gx-text-uppercase gx-text-underline custom-text gx-fs-2xl blend\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 21\n    }\n  }), ' ', \"Management System\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-app-login-container gx-z-index-20\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-app-login-main-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-app-login-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-w-100 gx-d-flex gx-align-items-center gx-justify-content-between\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-fs-xxl gx-text-center wy-typo-color gx-font-weight-medium\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 33\n    }\n  }, \"TMS -\", ' ', /*#__PURE__*/React.createElement(IntlMessages, {\n    id: \"app.userAuth.signIn\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 37\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-mt-2 gx-position-relative\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    alt: \"example\",\n    src: require('assets/images/wify_logo.png'),\n    style: {\n      maxWidth: '94px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 37\n    }\n  }), ' ')), /*#__PURE__*/React.createElement(Tabs, {\n    defaultActiveKey: \"email\",\n    centered: screenWidth < 767,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    key: \"email\",\n    tab: /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 41\n      }\n    }, /*#__PURE__*/React.createElement(MailOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 45\n      }\n    }), \" EMAIL\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Form, {\n    initialValues: {\n      remember: true\n    },\n    name: \"basic\",\n    onFinish: onFinish,\n    onFinishFailed: onFinishFailed,\n    className: \"gx-signin-form gx-form-row0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(Form.Item, {\n    initialValue: \"EMAIL\",\n    className: \"gx-d-none\",\n    name: \"id_type\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Input, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 45\n    }\n  })), /*#__PURE__*/React.createElement(Form.Item, {\n    initialValue: isProduction ? '' : '<EMAIL>',\n    rules: [{\n      required: true,\n      message: 'The input is not valid E-mail!'\n    }],\n    name: \"id\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Input, {\n    placeholder: \"Email\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 45\n    }\n  })), /*#__PURE__*/React.createElement(Form.Item, {\n    initialValue: isProduction ? '' : 'demo#123',\n    rules: [{\n      required: true,\n      message: 'Please input your Password!'\n    }],\n    name: \"id_key\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Input, {\n    type: \"password\",\n    placeholder: \"Password\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 45\n    }\n  })), /*#__PURE__*/React.createElement(Form.Item, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 41\n    }\n  }, \"By signing up to this app, you agree with the\", /*#__PURE__*/React.createElement(\"a\", {\n    href: `${website_url}/tms/privacypolicy`,\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    className: \"gx-signup-form-forgot gx-link gx-ml-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 45\n    }\n  }, \"Privacy Policy\")), /*#__PURE__*/React.createElement(Form.Item, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    className: \"gx-mb-0\",\n    htmlType: \"submit\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(IntlMessages, {\n    id: \"app.userAuth.signIn\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 49\n    }\n  }))))), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    key: \"mobile_num\",\n    tab: /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 41\n      }\n    }, /*#__PURE__*/React.createElement(MobileOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 45\n      }\n    }), \" MOBILE NO.\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Form, {\n    className: \"gx-signin-form gx-form-row0\",\n    layout: \"vertical\",\n    onFinish: onFinish,\n    onFinishFailed: onFinishFailed,\n    ref: mobileLoginFormRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(FormBuilder, {\n    meta: getMobilNumFormMeta(),\n    form: mobileLoginFormRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 41\n    }\n  }), /*#__PURE__*/React.createElement(Form.Item, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 41\n    }\n  }, \"By signing up to this app, you agree with the\", /*#__PURE__*/React.createElement(\"a\", {\n    href: `${website_url}/tms/privacypolicy`,\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    className: \"gx-signup-form-forgot gx-link gx-ml-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 45\n    }\n  }, \"Privacy Policy\")), /*#__PURE__*/React.createElement(Form.Item, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    htmlType: \"submit\",\n    className: \"gx-mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 45\n    }\n  }, \"Sign In\"))))), !isAndroidApp() && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"hr\", {\n    className: \"gx-mt-1 gx-mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 37\n    }\n  }), showPlayStoreIcon ? /*#__PURE__*/React.createElement(\"a\", {\n    href: tms_apk_download_link,\n    download: \"wify\",\n    className: \"wy-temp-apk-download-button\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"wy-temp-apk-download-section-wrapper gx-d-flex gx-align-items-center gx-justify-content-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(HiOutlineDownload, {\n    className: \"wy-temp-apk-download-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 53\n    }\n  })), \"Download Android APK\")) : /*#__PURE__*/React.createElement(\"a\", {\n    href: tms_apk_download_link,\n    download: \"wify\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: android_icon,\n    width: 50,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 45\n    }\n  }), \"Download app\")), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"gx-text-center gx-text-black gx-fs-md gx-mt-4  gx-text-bold\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 29\n    }\n  }, \"Copyright \\xA9 Wify Technology\"), is_multi_orgs && /*#__PURE__*/React.createElement(Modal, {\n    title: /*#__PURE__*/React.createElement(\"div\", {\n      className: \"wy-flex-responsive gx-align-items-start gx-align-items-md-center \",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 41\n      }\n    }, (multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length) <= 3 && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-mr-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 49\n      }\n    }, \"Choose one..\"), (multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length) > 3 && /*#__PURE__*/React.createElement(Input, {\n      ref: orgsSearchInputRef,\n      className: \"wy-login-search-brand-input\",\n      placeholder: \"Search by org name...\",\n      onChange: e => filterMultiOrgs(e, multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data),\n      allowClear: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 49\n      }\n    })),\n    visible: is_multi_orgs,\n    onCancel: handleCancel,\n    width: (multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length) == 2 ? 600 : 1000,\n    footer: null,\n    centered: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: brandContainerByCount(multi_orgs_payload === null || multi_orgs_payload === void 0 ? void 0 : multi_orgs_payload.resp.data.length),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(List, {\n    grid: getGridViewCountData()\n    // itemLayout=\"horizontal\"\n    ,\n    className: \"gx-mr-2\",\n    dataSource: filteredOrgArray ? filteredOrgArray : multi_orgs_payload.resp.data,\n    renderItem: item => /*#__PURE__*/React.createElement(List.Item, {\n      key: item.id,\n      onClick: e => handleOrgSelect(item.org_id),\n      className: \"gx-task-list-item wy-brands-wrapper\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(List.Item.Meta, {\n      title: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"gx-position-relative\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 61\n        }\n      }, item.designation && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 69\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"wy-user-designation gx-mb-2 gx-fs-11 wy-brands-animated-text wy-brands-designation gx-d-flex gx-align-items-center gx-justify-content-start\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 73\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"icon icon-user-o gx-mr-1\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 77\n        }\n      }), ' ', item.designation, ' ')), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"wy-gap-0 wy-flex-responsive-reverse wy-brands-mobile-padding \",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 65\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 69\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"wy-brands-logo-wrapper wy-gradient-border\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 73\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"wy-inner-content\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 77\n        }\n      }, /*#__PURE__*/React.createElement(\"img\", {\n        src: item.icon,\n        className: \"wy-object-contain  wy-center-to-div\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 81\n        }\n      })))), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"gx-d-block gx-d-md-none\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 69\n        }\n      }, /*#__PURE__*/React.createElement(\"h4\", {\n        className: \"gx-mb-0 wy-brands-animated-text\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 73\n        }\n      }, ' ', item.name, ' '), /*#__PURE__*/React.createElement(\"small\", {\n        className: \"wy-brands-animated-text\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 73\n        }\n      }, item.name_legal, ' ')), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"gx-d-none gx-d-md-block\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 69\n        }\n      }, /*#__PURE__*/React.createElement(\"h4\", {\n        className: \"gx-mb-0 wy-brands-animated-text\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 73\n        }\n      }, /*#__PURE__*/React.createElement(Text, {\n        className: ellipsis ? 'wy-brand-text-ellipsis' : undefined,\n        ellipsis: ellipsis ? {\n          tooltip: item.name\n        } : false,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 77\n        }\n      }, item.name), ' '), ' ', /*#__PURE__*/React.createElement(\"small\", {\n        className: \"wy-brands-animated-text\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 73\n        }\n      }, /*#__PURE__*/React.createElement(Text, {\n        className: ellipsis ? 'wy-brand-text-ellipsis' : null,\n        ellipsis: ellipsis ? {\n          tooltip: item.name_legal\n        } : false,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 77\n        }\n      }, item.name_legal))))),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 53\n      }\n    })),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 589,\n      columnNumber: 41\n    }\n  })))), /*#__PURE__*/React.createElement(InfoView, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 25\n    }\n  }))));\n};\nexport default SignIn;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "<PERSON><PERSON>", "Checkbox", "Form", "Input", "Tabs", "Statistic", "Modal", "List", "Avatar", "message", "Typography", "useDispatch", "useSelector", "MailOutlined", "MobileOutlined", "PhoneOutlined", "SyncOutlined", "superAdminSignIn", "userSignIn", "IntlMessages", "InfoView", "FormBuilder", "number", "SELECT_ORG_FOR_LOGIN", "isAndroidApp", "isMobile<PERSON>iew", "setHtmlHeadTitle", "android_icon", "android_play_icon", "TypeAnimation", "LaborDay", "HiOutlineDownload", "Countdown", "Text", "tms_apk_download_link", "process", "env", "REACT_APP_TMS_APK_LINK_TO_DOWNLOAD", "website_url", "REACT_APP_WEBSITE_URL", "SignIn", "props", "_props$location4", "_props$location4$stat", "_props$location4$stat2", "timeOfDay", "setTimeOfDay", "getTimeOfDay", "screenWidth", "setScreenWidth", "window", "innerWidth", "renderHelper", "set<PERSON><PERSON>Helper", "superAdminAccessFlag", "setsuperAdminAccessFlag", "ellipsis", "set<PERSON><PERSON><PERSON>", "filteredOrgArray", "setFilteredOrgArray", "dispatch", "mobileLoginFormRef", "token", "auth", "otp_sent", "otp_expiry", "is_multi_orgs", "multi_orgs_payload", "orgsSearchInputRef", "resp", "data", "length", "current", "focus", "location", "pathname", "onFinishFailed", "errorInfo", "console", "log", "onResendOTPClick", "form_data", "getFieldsValue", "id_key", "undefined", "onFinish", "handleCancel", "payload", "type", "handleOrgSelect", "org_id", "id_type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newFormData", "values", "onOTPCountDownFinish", "brandContainerByCount", "orgListLength", "getMobilNumFormMeta", "enter_otp_meta", "key", "colSpan", "placeholder", "rules", "required", "pattern", "RegExp", "min", "max", "render", "createElement", "Fragment", "Math", "floor", "Date", "now", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "title", "format", "value", "onClick", "e", "meta", "columns", "formItemLayout", "fields", "initialValue", "superAdminParams", "decodeParamsFrSuprAdminLogin", "_props$location", "_props$location$state", "_props$location$state2", "state", "from", "search", "_props$location2", "_props$location2$stat", "_props$location2$stat2", "_props$location3", "_props$location3$stat", "_props$location3$stat2", "history", "push", "searchParams", "URLSearchParams", "user_id", "get", "my_token", "is_check_technician_app", "id", "currentTime", "hours", "getHours", "filterMultiOrgs", "searchEvent", "multiOrgsData", "searchString", "target", "filteredOrgsList", "filter", "item", "name", "toLowerCase", "includes", "getGridViewCountData", "gutter", "xs", "sm", "md", "lg", "xl", "xxl", "isProduction", "NODE_ENV", "showPlayStoreIcon", "REACT_APP_PLAYSTORE", "sequence", "speed", "repeat", "Infinity", "alt", "src", "require", "style", "max<PERSON><PERSON><PERSON>", "defaultActiveKey", "centered", "TabPane", "tab", "initialValues", "remember", "<PERSON><PERSON>", "href", "rel", "htmlType", "layout", "ref", "form", "download", "width", "onChange", "allowClear", "visible", "onCancel", "footer", "grid", "dataSource", "renderItem", "Meta", "designation", "icon", "name_legal", "tooltip"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/containers/SignIn.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\r\nimport {\r\n    Button,\r\n    Checkbox,\r\n    Form,\r\n    Input,\r\n    Tabs,\r\n    Statistic,\r\n    Modal,\r\n    List,\r\n    Avatar,\r\n    message,\r\n    Typography,\r\n} from 'antd';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport {\r\n    MailOutlined,\r\n    MobileOutlined,\r\n    PhoneOutlined,\r\n    SyncOutlined,\r\n} from '@ant-design/icons';\r\nimport { superAdminSignIn, userSignIn } from '../appRedux/actions/Auth';\r\nimport IntlMessages from 'util/IntlMessages';\r\nimport InfoView from 'components/InfoView';\r\nimport FormBuilder from 'antd-form-builder';\r\nimport { number } from 'prop-types';\r\nimport { SELECT_ORG_FOR_LOGIN } from '../constants/ActionTypes';\r\nimport { isAndroidApp, isMobileView, setHtmlHeadTitle } from '../util/helpers';\r\nimport android_icon from '../assets/images/android_icon.png';\r\nimport android_play_icon from '../assets/images/google_play_icon.svg';\r\nimport { TypeAnimation } from 'react-type-animation';\r\nimport LaborDay from '../components/Aesthetics/LaborDay';\r\nimport { HiOutlineDownload } from 'react-icons/hi';\r\n\r\nconst { Countdown } = Statistic;\r\nconst { Text } = Typography;\r\n\r\nconst tms_apk_download_link = process.env.REACT_APP_TMS_APK_LINK_TO_DOWNLOAD;\r\nconst website_url =\r\n    process.env.REACT_APP_WEBSITE_URL || 'https://home.wify.co.in';\r\n\r\nconst SignIn = (props) => {\r\n    // const {render_helper} = useState()\r\n    const [timeOfDay, setTimeOfDay] = useState(getTimeOfDay());\r\n    const [screenWidth, setScreenWidth] = useState(window.innerWidth);\r\n    const [renderHelper, setRenderHelper] = useState(false);\r\n    const [superAdminAccessFlag, setsuperAdminAccessFlag] = useState(false);\r\n    const [ellipsis, setEllipsis] = useState(true);\r\n    const [filteredOrgArray, setFilteredOrgArray] = useState(null);\r\n    const dispatch = useDispatch();\r\n    const mobileLoginFormRef = useRef();\r\n    const token = useSelector(({ auth }) => auth.token);\r\n\r\n    const otp_sent = useSelector(({ auth }) => auth.otp_sent);\r\n    const otp_expiry = useSelector(({ auth }) => auth.otp_expiry);\r\n\r\n    const is_multi_orgs = useSelector(({ auth }) => auth.is_multi_orgs);\r\n    const multi_orgs_payload = useSelector(\r\n        ({ auth }) => auth.multi_orgs_payload\r\n    );\r\n\r\n    const orgsSearchInputRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        if (is_multi_orgs && multi_orgs_payload?.resp.data.length > 3) {\r\n            orgsSearchInputRef.current.focus();\r\n        }\r\n\r\n        setHtmlHeadTitle(props.location.pathname);\r\n    }, [is_multi_orgs]);\r\n\r\n    // console.log(\"multi_orgs_payload\",multi_orgs_payload);\r\n    const onFinishFailed = (errorInfo) => {\r\n        console.log('Failed:', errorInfo);\r\n    };\r\n\r\n    const onResendOTPClick = () => {\r\n        let form_data = mobileLoginFormRef.current.getFieldsValue();\r\n        form_data.id_key = undefined;\r\n        onFinish(form_data);\r\n    };\r\n\r\n    const handleCancel = () => {\r\n        let payload = {\r\n            ...multi_orgs_payload,\r\n            resp: undefined,\r\n        };\r\n        setFilteredOrgArray(null);\r\n        dispatch({ type: SELECT_ORG_FOR_LOGIN, payload: payload });\r\n    };\r\n\r\n    const handleOrgSelect = (org_id) => {\r\n        if (multi_orgs_payload.form_data.id_type == 'MOBILE_NUM') {\r\n            mobileLoginFormRef.current.setFieldsValue({\r\n                org_id: org_id,\r\n            });\r\n        }\r\n        // message.success('seelcted org ' + org_id);\r\n        let newFormData = {\r\n            ...multi_orgs_payload.form_data,\r\n            org_id: org_id,\r\n        };\r\n        onFinish(newFormData);\r\n        dispatch({ type: SELECT_ORG_FOR_LOGIN, payload: undefined });\r\n    };\r\n\r\n    const onFinish = (values) => {\r\n        // dispatch({type:FETCH_START});\r\n        setFilteredOrgArray(null);\r\n        dispatch(userSignIn(values));\r\n    };\r\n\r\n    const onOTPCountDownFinish = () => {\r\n        setRenderHelper(!renderHelper);\r\n    };\r\n\r\n    // Function to determine the CSS class for the brand container based on the number of organizations.\r\n    const brandContainerByCount = (orgListLength) => {\r\n        // Check if the number of organizations is greater than 9.\r\n        if (orgListLength > 9) {\r\n            // If more than 9, apply custom scroll bar and list container styling.\r\n            return 'wy-scroll-bar-custom wy-brands-list-container';\r\n        }\r\n        // Check if the number of organizations is exactly 9.\r\n        else if (orgListLength == 9) {\r\n            // If exactly 9, apply a specific styling for the count of nine.\r\n            return 'wy-brands-list-count-nine';\r\n        }\r\n        // Check if the number of organizations is less than 9 but greater than 6.\r\n        else if (orgListLength < 9 && orgListLength > 6) {\r\n            // If between 7 and 9, apply a specific styling for counts from six to nine.\r\n            return 'wy-brands-list-count-six-to-nine';\r\n        }\r\n        // Check if the number of organizations is less than 6 but greater than 3.\r\n        else if (orgListLength < 6 && orgListLength > 3) {\r\n            // If between 4 and 6, apply a specific styling for counts from three to six.\r\n            return 'wy-brands-list-count-three-to-six';\r\n        }\r\n        // Check if the number of organizations is 3 or fewer.\r\n        else if (orgListLength <= 3) {\r\n            // If 3 or fewer, apply a specific styling for counts up to three.\r\n            return 'wy-brands-list-count-three';\r\n        }\r\n        // In case none of the above conditions are met, return an empty string.\r\n        else {\r\n            return '';\r\n        }\r\n    };\r\n\r\n    const getMobilNumFormMeta = () => {\r\n        const enter_otp_meta = [\r\n            {\r\n                key: 'id_key',\r\n                type: 'number',\r\n                colSpan: 1,\r\n                placeholder: 'Enter OTP',\r\n                rules: [\r\n                    {\r\n                        required: true,\r\n                        pattern: new RegExp('^[0-9]*$'),\r\n                        message: 'Enter your right OTP',\r\n                    },\r\n                    { min: 4, message: 'OTP must be min 4 digits.' },\r\n                    { max: 4, message: 'OTP must be max 4 digits.' },\r\n                ],\r\n            },\r\n            {\r\n                key: 'retry',\r\n                colSpan: 1,\r\n                render() {\r\n                    return (\r\n                        <>\r\n                            {otp_expiry &&\r\n                            otp_expiry > Math.floor(Date.now()) ? (\r\n                                <span>\r\n                                    Retry in{' '}\r\n                                    <Countdown\r\n                                        className=\"gx-d-inline-block\"\r\n                                        title={null}\r\n                                        format=\"ss\"\r\n                                        value={otp_expiry}\r\n                                        onFinish={onOTPCountDownFinish}\r\n                                    />\r\n                                    s\r\n                                </span>\r\n                            ) : (\r\n                                <Button\r\n                                    type=\"link\"\r\n                                    onClick={(e) => onResendOTPClick()}\r\n                                >\r\n                                    <SyncOutlined /> Resend OTP\r\n                                </Button>\r\n                            )}\r\n                        </>\r\n                    );\r\n                },\r\n            },\r\n        ];\r\n        const meta = {\r\n            columns: 2,\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: 'id_type',\r\n                    colSpan: 1,\r\n                    initialValue: 'MOBILE_NUM',\r\n                    className: 'gx-d-none',\r\n                },\r\n                {\r\n                    key: 'org_id',\r\n                    colSpan: 1,\r\n                    className: 'gx-d-none',\r\n                },\r\n                {\r\n                    key: 'id',\r\n                    colSpan: 2,\r\n                    type: 'number',\r\n                    placeholder: 'Mobile no.',\r\n                    rules: [\r\n                        {\r\n                            required: true,\r\n                            pattern: new RegExp('^[0-9]*$'),\r\n                            message: 'Please enter your right mobile number!',\r\n                        },\r\n                        {\r\n                            min: 10,\r\n                            message: 'Mobile no must be min 10 digits.',\r\n                        },\r\n                        {\r\n                            max: 10,\r\n                            message: 'Mobile no must be max 10 digits.',\r\n                        },\r\n                    ],\r\n                },\r\n                ...(otp_sent ? enter_otp_meta : ''),\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (!superAdminAccessFlag) {\r\n            let superAdminParams = decodeParamsFrSuprAdminLogin();\r\n            if (superAdminParams) {\r\n                setsuperAdminAccessFlag(true);\r\n                dispatch(userSignIn(superAdminParams));\r\n                return;\r\n            }\r\n        }\r\n        if (token !== null) {\r\n            if (props?.location?.state?.from?.search) {\r\n                props.history.push(\r\n                    `${props?.location?.state?.from?.pathname}${props?.location?.state?.from?.search}`\r\n                );\r\n            } else {\r\n                props.history.push('/');\r\n            }\r\n        }\r\n    }, [token, props.history, props?.location?.state?.from?.search]);\r\n\r\n    const decodeParamsFrSuprAdminLogin = () => {\r\n        let searchParams = new URLSearchParams(window.location.search);\r\n        let user_id = searchParams.get('user_id');\r\n        let my_token = searchParams.get('my_token');\r\n        let is_check_technician_app = true;\r\n        if (user_id && my_token) {\r\n            return {\r\n                id_type: 'SUPER_ADMIN_ACCESS',\r\n                id: user_id,\r\n                id_key: my_token,\r\n                is_check_technician_app,\r\n            };\r\n        }\r\n    };\r\n\r\n    function getTimeOfDay() {\r\n        const currentTime = new Date();\r\n        const hours = currentTime.getHours();\r\n        if (hours >= 5 && hours < 12) {\r\n            return 'morning';\r\n        } else if (hours >= 12 && hours < 16) {\r\n            return 'afternoon';\r\n        } else if (hours >= 16 && hours < 19) {\r\n            return 'evening';\r\n        } else {\r\n            return 'night';\r\n        }\r\n    }\r\n    useEffect(() => {\r\n        setTimeOfDay(getTimeOfDay());\r\n    }, []);\r\n\r\n    // Function to filter a list of organizations based on a search event's input.\r\n    const filterMultiOrgs = (searchEvent, multiOrgsData) => {\r\n        // Retrieve the search string entered by the user from the event target's value.\r\n        let searchString = searchEvent.target.value;\r\n\r\n        // Declare a variable to hold the filtered list of organizations.\r\n        let filteredOrgsList;\r\n\r\n        // Check if the search string has a length greater than 0, meaning the user has entered some text.\r\n        if (searchString.length > 0) {\r\n            // Filter the list of organizations by checking if the organization's name includes the search string.\r\n            // Convert both the organization's name and the search string to lowercase to make the search case-insensitive.\r\n            filteredOrgsList = multiOrgsData.filter((item) =>\r\n                item.name.toLowerCase().includes(searchString.toLowerCase())\r\n            );\r\n        }\r\n\r\n        // Update the state that stores the filtered list of organizations.\r\n        setFilteredOrgArray(filteredOrgsList);\r\n    };\r\n\r\n    const getGridViewCountData = () => {\r\n        return {\r\n            gutter: 16,\r\n            xs: 1,\r\n            sm: 2,\r\n            md: 3,\r\n            lg: multi_orgs_payload?.resp.data.length == 2 ? 2 : 3,\r\n            xl: multi_orgs_payload?.resp.data.length == 2 ? 2 : 3,\r\n            xxl: multi_orgs_payload?.resp.data.length == 2 ? 2 : 3,\r\n        };\r\n    };\r\n\r\n    const isProduction = process.env.NODE_ENV == 'production';\r\n    const showPlayStoreIcon = process.env.REACT_APP_PLAYSTORE == 'true';\r\n    return (\r\n        !superAdminAccessFlag && (\r\n            <div\r\n                className={`gx-app-login-wrap ${timeOfDay} gx-overflow-hidden`}\r\n            >\r\n                <div className=\"animation-wrapper\">\r\n                    <div className=\"particle particle-1\"></div>\r\n                    <div className=\"particle particle-2\"></div>\r\n                    <div className=\"particle particle-3\"></div>\r\n                    <div className=\"particle particle-4\"></div>\r\n                </div>\r\n                <h3\r\n                    className={` gx-mt-3 custom-text blend gx-z-index-20 ${timeOfDay == 'morning' ? 'gx-text-black' : 'gx-text-white'}`}\r\n                >\r\n                    Your{' '}\r\n                    <TypeAnimation\r\n                        sequence={[\r\n                            'Time',\r\n                            3000,\r\n                            'Task',\r\n                            3000,\r\n                            'Ticket',\r\n                            3000,\r\n                            'Team',\r\n                            3000,\r\n                        ]}\r\n                        speed={250}\r\n                        repeat={Infinity}\r\n                        className=\"gx-text-uppercase gx-text-underline custom-text gx-fs-2xl blend\"\r\n                    />{' '}\r\n                    Management System\r\n                </h3>\r\n                <div className=\"gx-app-login-container gx-z-index-20\">\r\n                    <div className=\"gx-app-login-main-content\">\r\n                        <div className=\"gx-app-login-content\">\r\n                            <div className=\"gx-w-100 gx-d-flex gx-align-items-center gx-justify-content-between\">\r\n                                <div className=\"gx-fs-xxl gx-text-center wy-typo-color gx-font-weight-medium\">\r\n                                    TMS -{' '}\r\n                                    <IntlMessages id=\"app.userAuth.signIn\" />\r\n                                </div>\r\n                                <div className=\"gx-mt-2 gx-position-relative\">\r\n                                    <img\r\n                                        alt=\"example\"\r\n                                        src={require('assets/images/wify_logo.png')}\r\n                                        style={{ maxWidth: '94px' }}\r\n                                    />{' '}\r\n                                </div>\r\n                            </div>\r\n                            <Tabs\r\n                                defaultActiveKey=\"email\"\r\n                                centered={screenWidth < 767}\r\n                            >\r\n                                <Tabs.TabPane\r\n                                    key=\"email\"\r\n                                    tab={\r\n                                        <span>\r\n                                            <MailOutlined /> EMAIL\r\n                                        </span>\r\n                                    }\r\n                                >\r\n                                    <Form\r\n                                        initialValues={{ remember: true }}\r\n                                        name=\"basic\"\r\n                                        onFinish={onFinish}\r\n                                        onFinishFailed={onFinishFailed}\r\n                                        className=\"gx-signin-form gx-form-row0\"\r\n                                    >\r\n                                        <Form.Item\r\n                                            initialValue=\"EMAIL\"\r\n                                            className=\"gx-d-none\"\r\n                                            name=\"id_type\"\r\n                                        >\r\n                                            <Input />\r\n                                        </Form.Item>\r\n                                        <Form.Item\r\n                                            initialValue={\r\n                                                isProduction\r\n                                                    ? ''\r\n                                                    : '<EMAIL>'\r\n                                            }\r\n                                            rules={[\r\n                                                {\r\n                                                    required: true,\r\n                                                    message:\r\n                                                        'The input is not valid E-mail!',\r\n                                                },\r\n                                            ]}\r\n                                            name=\"id\"\r\n                                        >\r\n                                            <Input placeholder=\"Email\" />\r\n                                        </Form.Item>\r\n                                        <Form.Item\r\n                                            initialValue={\r\n                                                isProduction ? '' : 'demo#123'\r\n                                            }\r\n                                            rules={[\r\n                                                {\r\n                                                    required: true,\r\n                                                    message:\r\n                                                        'Please input your Password!',\r\n                                                },\r\n                                            ]}\r\n                                            name=\"id_key\"\r\n                                        >\r\n                                            <Input\r\n                                                type=\"password\"\r\n                                                placeholder=\"Password\"\r\n                                            />\r\n                                        </Form.Item>\r\n                                        <Form.Item>\r\n                                            By signing up to this app, you agree\r\n                                            with the\r\n                                            <a\r\n                                                href={`${website_url}/tms/privacypolicy`}\r\n                                                target=\"_blank\"\r\n                                                rel=\"noopener noreferrer\"\r\n                                                className=\"gx-signup-form-forgot gx-link gx-ml-2\"\r\n                                            >\r\n                                                Privacy Policy\r\n                                            </a>\r\n                                        </Form.Item>\r\n                                        <Form.Item>\r\n                                            <Button\r\n                                                type=\"primary\"\r\n                                                className=\"gx-mb-0\"\r\n                                                htmlType=\"submit\"\r\n                                            >\r\n                                                <IntlMessages id=\"app.userAuth.signIn\" />\r\n                                            </Button>\r\n                                            {/* <span><IntlMessages id=\"app.userAuth.or\"/></span> \r\n                {/* <span><IntlMessages id=\"app.userAuth.or\"/></span> \r\n                    {/* <span><IntlMessages id=\"app.userAuth.or\"/></span> \r\n                    <Link to=\"/signup\"><IntlMessages\r\n                    id=\"app.userAuth.signUp\"/></Link> */}\r\n                                        </Form.Item>\r\n                                    </Form>\r\n                                </Tabs.TabPane>\r\n                                <Tabs.TabPane\r\n                                    key=\"mobile_num\"\r\n                                    tab={\r\n                                        <span>\r\n                                            <MobileOutlined /> MOBILE NO.\r\n                                        </span>\r\n                                    }\r\n                                >\r\n                                    <Form\r\n                                        className=\"gx-signin-form gx-form-row0\"\r\n                                        layout=\"vertical\"\r\n                                        onFinish={onFinish}\r\n                                        onFinishFailed={onFinishFailed}\r\n                                        ref={mobileLoginFormRef}\r\n                                    >\r\n                                        <FormBuilder\r\n                                            meta={getMobilNumFormMeta()}\r\n                                            form={mobileLoginFormRef}\r\n                                        />\r\n                                        <Form.Item>\r\n                                            By signing up to this app, you agree\r\n                                            with the\r\n                                            <a\r\n                                                href={`${website_url}/tms/privacypolicy`}\r\n                                                target=\"_blank\"\r\n                                                rel=\"noopener noreferrer\"\r\n                                                className=\"gx-signup-form-forgot gx-link gx-ml-2\"\r\n                                            >\r\n                                                Privacy Policy\r\n                                            </a>\r\n                                        </Form.Item>\r\n                                        <Form.Item>\r\n                                            <Button\r\n                                                type=\"primary\"\r\n                                                htmlType=\"submit\"\r\n                                                className=\"gx-mb-0\"\r\n                                            >\r\n                                                Sign In\r\n                                            </Button>\r\n                                        </Form.Item>\r\n                                    </Form>\r\n                                </Tabs.TabPane>\r\n                            </Tabs>\r\n                            {!isAndroidApp() && (\r\n                                <div className=\"gx-text-center\">\r\n                                    <hr className=\"gx-mt-1 gx-mb-4\" />\r\n                                    {showPlayStoreIcon ? (\r\n                                        <a\r\n                                            href={tms_apk_download_link}\r\n                                            download=\"wify\"\r\n                                            className=\"wy-temp-apk-download-button\"\r\n                                        >\r\n                                            {/* <img\r\n                                                src={android_play_icon}\r\n                                                width={150}\r\n                                            /> */}\r\n                                            <span className=\"wy-temp-apk-download-section-wrapper gx-d-flex gx-align-items-center gx-justify-content-center\">\r\n                                                <span>\r\n                                                    <HiOutlineDownload className=\"wy-temp-apk-download-icon\" />\r\n                                                </span>\r\n                                                Download Android APK\r\n                                            </span>\r\n                                        </a>\r\n                                    ) : (\r\n                                        <a\r\n                                            href={tms_apk_download_link}\r\n                                            download=\"wify\"\r\n                                        >\r\n                                            <img\r\n                                                src={android_icon}\r\n                                                width={50}\r\n                                            />\r\n                                            Download app\r\n                                        </a>\r\n                                    )}\r\n                                </div>\r\n                            )}\r\n                            <p className=\"gx-text-center gx-text-black gx-fs-md gx-mt-4  gx-text-bold\">\r\n                                Copyright © Wify Technology\r\n                            </p>\r\n                            {is_multi_orgs && (\r\n                                <Modal\r\n                                    title={\r\n                                        <div className=\"wy-flex-responsive gx-align-items-start gx-align-items-md-center \">\r\n                                            {multi_orgs_payload?.resp.data\r\n                                                .length <= 3 && (\r\n                                                <div className=\"gx-mr-2\">\r\n                                                    Choose one..\r\n                                                </div>\r\n                                            )}\r\n                                            {multi_orgs_payload?.resp.data\r\n                                                .length > 3 && (\r\n                                                <Input\r\n                                                    ref={orgsSearchInputRef}\r\n                                                    className=\"wy-login-search-brand-input\"\r\n                                                    placeholder=\"Search by org name...\"\r\n                                                    onChange={(e) =>\r\n                                                        filterMultiOrgs(\r\n                                                            e,\r\n                                                            multi_orgs_payload\r\n                                                                ?.resp.data\r\n                                                        )\r\n                                                    }\r\n                                                    allowClear\r\n                                                />\r\n                                            )}\r\n                                        </div>\r\n                                    }\r\n                                    visible={is_multi_orgs}\r\n                                    onCancel={handleCancel}\r\n                                    width={\r\n                                        multi_orgs_payload?.resp.data.length ==\r\n                                        2\r\n                                            ? 600\r\n                                            : 1000\r\n                                    }\r\n                                    footer={null}\r\n                                    centered={true}\r\n                                >\r\n                                    <div\r\n                                        className={brandContainerByCount(\r\n                                            multi_orgs_payload?.resp.data.length\r\n                                        )}\r\n                                    >\r\n                                        <List\r\n                                            grid={getGridViewCountData()}\r\n                                            // itemLayout=\"horizontal\"\r\n                                            className=\"gx-mr-2\"\r\n                                            dataSource={\r\n                                                filteredOrgArray\r\n                                                    ? filteredOrgArray\r\n                                                    : multi_orgs_payload.resp\r\n                                                          .data\r\n                                            }\r\n                                            renderItem={(item) => (\r\n                                                <List.Item\r\n                                                    key={item.id}\r\n                                                    onClick={(e) =>\r\n                                                        handleOrgSelect(\r\n                                                            item.org_id\r\n                                                        )\r\n                                                    }\r\n                                                    className=\"gx-task-list-item wy-brands-wrapper\"\r\n                                                >\r\n                                                    <List.Item.Meta\r\n                                                        title={\r\n                                                            <div className=\"gx-position-relative\">\r\n                                                                {item.designation && (\r\n                                                                    <div className=\"\">\r\n                                                                        <div className=\"wy-user-designation gx-mb-2 gx-fs-11 wy-brands-animated-text wy-brands-designation gx-d-flex gx-align-items-center gx-justify-content-start\">\r\n                                                                            <div className=\"icon icon-user-o gx-mr-1\"></div>{' '}\r\n                                                                            {\r\n                                                                                item.designation\r\n                                                                            }{' '}\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                )}\r\n                                                                <div className=\"wy-gap-0 wy-flex-responsive-reverse wy-brands-mobile-padding \">\r\n                                                                    <div>\r\n                                                                        <div className=\"wy-brands-logo-wrapper wy-gradient-border\">\r\n                                                                            <div className=\"wy-inner-content\">\r\n                                                                                <img\r\n                                                                                    src={\r\n                                                                                        item.icon\r\n                                                                                    }\r\n                                                                                    className=\"wy-object-contain  wy-center-to-div\"\r\n                                                                                />\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    <div className=\"gx-d-block gx-d-md-none\">\r\n                                                                        <h4 className=\"gx-mb-0 wy-brands-animated-text\">\r\n                                                                            {' '}\r\n                                                                            {\r\n                                                                                item.name\r\n                                                                            }{' '}\r\n                                                                        </h4>\r\n                                                                        <small className=\"wy-brands-animated-text\">\r\n                                                                            {\r\n                                                                                item.name_legal\r\n                                                                            }{' '}\r\n                                                                        </small>\r\n                                                                    </div>\r\n                                                                    <div className=\"gx-d-none gx-d-md-block\">\r\n                                                                        <h4 className=\"gx-mb-0 wy-brands-animated-text\">\r\n                                                                            <Text\r\n                                                                                className={\r\n                                                                                    ellipsis\r\n                                                                                        ? 'wy-brand-text-ellipsis'\r\n                                                                                        : undefined\r\n                                                                                }\r\n                                                                                ellipsis={\r\n                                                                                    ellipsis\r\n                                                                                        ? {\r\n                                                                                              tooltip:\r\n                                                                                                  item.name,\r\n                                                                                          }\r\n                                                                                        : false\r\n                                                                                }\r\n                                                                            >\r\n                                                                                {\r\n                                                                                    item.name\r\n                                                                                }\r\n                                                                            </Text>{' '}\r\n                                                                        </h4>{' '}\r\n                                                                        <small className=\"wy-brands-animated-text\">\r\n                                                                            <Text\r\n                                                                                className={\r\n                                                                                    ellipsis\r\n                                                                                        ? 'wy-brand-text-ellipsis'\r\n                                                                                        : null\r\n                                                                                }\r\n                                                                                ellipsis={\r\n                                                                                    ellipsis\r\n                                                                                        ? {\r\n                                                                                              tooltip:\r\n                                                                                                  item.name_legal,\r\n                                                                                          }\r\n                                                                                        : false\r\n                                                                                }\r\n                                                                            >\r\n                                                                                {\r\n                                                                                    item.name_legal\r\n                                                                                }\r\n                                                                            </Text>\r\n                                                                        </small>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        }\r\n                                                    />\r\n                                                </List.Item>\r\n                                            )}\r\n                                        />\r\n                                    </div>\r\n                                </Modal>\r\n                            )}\r\n                        </div>\r\n                        <InfoView />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        )\r\n    );\r\n};\r\n\r\nexport default SignIn;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SACIC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,UAAU,QACP,MAAM;AACb,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACIC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,YAAY,QACT,mBAAmB;AAC1B,SAASC,gBAAgB,EAAEC,UAAU,QAAQ,0BAA0B;AACvE,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC9E,OAAOC,YAAY,MAAM,mCAAmC;AAAC,OAAAC,iBAAA;AAE7D,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,SAASC,iBAAiB,QAAQ,gBAAgB;AAElD,MAAM;EAAEC;AAAU,CAAC,GAAG3B,SAAS;AAC/B,MAAM;EAAE4B;AAAK,CAAC,GAAGvB,UAAU;AAE3B,MAAMwB,qBAAqB,GAAGC,OAAO,CAACC,GAAG,CAACC,kCAAkC;AAC5E,MAAMC,WAAW,GACbH,OAAO,CAACC,GAAG,CAACG,qBAAqB,IAAI,yBAAyB;AAElE,MAAMC,MAAM,GAAIC,KAAK,IAAK;EAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACtB;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAACgD,YAAY,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAACmD,MAAM,CAACC,UAAU,CAAC;EACjE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM6D,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMkD,kBAAkB,GAAG/D,MAAM,CAAC,CAAC;EACnC,MAAMgE,KAAK,GAAGlD,WAAW,CAAC,CAAC;IAAEmD;EAAK,CAAC,KAAKA,IAAI,CAACD,KAAK,CAAC;EAEnD,MAAME,QAAQ,GAAGpD,WAAW,CAAC,CAAC;IAAEmD;EAAK,CAAC,KAAKA,IAAI,CAACC,QAAQ,CAAC;EACzD,MAAMC,UAAU,GAAGrD,WAAW,CAAC,CAAC;IAAEmD;EAAK,CAAC,KAAKA,IAAI,CAACE,UAAU,CAAC;EAE7D,MAAMC,aAAa,GAAGtD,WAAW,CAAC,CAAC;IAAEmD;EAAK,CAAC,KAAKA,IAAI,CAACG,aAAa,CAAC;EACnE,MAAMC,kBAAkB,GAAGvD,WAAW,CAClC,CAAC;IAAEmD;EAAK,CAAC,KAAKA,IAAI,CAACI,kBACvB,CAAC;EAED,MAAMC,kBAAkB,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAEvCD,SAAS,CAAC,MAAM;IACZ,IAAIqE,aAAa,IAAI,CAAAC,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CAACC,MAAM,IAAG,CAAC,EAAE;MAC3DH,kBAAkB,CAACI,OAAO,CAACC,KAAK,CAAC,CAAC;IACtC;IAEA/C,gBAAgB,CAACe,KAAK,CAACiC,QAAQ,CAACC,QAAQ,CAAC;EAC7C,CAAC,EAAE,CAACT,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMU,cAAc,GAAIC,SAAS,IAAK;IAClCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,SAAS,CAAC;EACrC,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAIC,SAAS,GAAGpB,kBAAkB,CAACW,OAAO,CAACU,cAAc,CAAC,CAAC;IAC3DD,SAAS,CAACE,MAAM,GAAGC,SAAS;IAC5BC,QAAQ,CAACJ,SAAS,CAAC;EACvB,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIC,OAAO,GAAG;MACV,GAAGpB,kBAAkB;MACrBE,IAAI,EAAEe;IACV,CAAC;IACDzB,mBAAmB,CAAC,IAAI,CAAC;IACzBC,QAAQ,CAAC;MAAE4B,IAAI,EAAEjE,oBAAoB;MAAEgE,OAAO,EAAEA;IAAQ,CAAC,CAAC;EAC9D,CAAC;EAED,MAAME,eAAe,GAAIC,MAAM,IAAK;IAChC,IAAIvB,kBAAkB,CAACc,SAAS,CAACU,OAAO,IAAI,YAAY,EAAE;MACtD9B,kBAAkB,CAACW,OAAO,CAACoB,cAAc,CAAC;QACtCF,MAAM,EAAEA;MACZ,CAAC,CAAC;IACN;IACA;IACA,IAAIG,WAAW,GAAG;MACd,GAAG1B,kBAAkB,CAACc,SAAS;MAC/BS,MAAM,EAAEA;IACZ,CAAC;IACDL,QAAQ,CAACQ,WAAW,CAAC;IACrBjC,QAAQ,CAAC;MAAE4B,IAAI,EAAEjE,oBAAoB;MAAEgE,OAAO,EAAEH;IAAU,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,QAAQ,GAAIS,MAAM,IAAK;IACzB;IACAnC,mBAAmB,CAAC,IAAI,CAAC;IACzBC,QAAQ,CAAC1C,UAAU,CAAC4E,MAAM,CAAC,CAAC;EAChC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IAC/B1C,eAAe,CAAC,CAACD,YAAY,CAAC;EAClC,CAAC;;EAED;EACA,MAAM4C,qBAAqB,GAAIC,aAAa,IAAK;IAC7C;IACA,IAAIA,aAAa,GAAG,CAAC,EAAE;MACnB;MACA,OAAO,+CAA+C;IAC1D;IACA;IAAA,KACK,IAAIA,aAAa,IAAI,CAAC,EAAE;MACzB;MACA,OAAO,2BAA2B;IACtC;IACA;IAAA,KACK,IAAIA,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC7C;MACA,OAAO,kCAAkC;IAC7C;IACA;IAAA,KACK,IAAIA,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC7C;MACA,OAAO,mCAAmC;IAC9C;IACA;IAAA,KACK,IAAIA,aAAa,IAAI,CAAC,EAAE;MACzB;MACA,OAAO,4BAA4B;IACvC;IACA;IAAA,KACK;MACD,OAAO,EAAE;IACb;EACJ,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,cAAc,GAAG,CACnB;MACIC,GAAG,EAAE,QAAQ;MACbZ,IAAI,EAAE,QAAQ;MACda,OAAO,EAAE,CAAC;MACVC,WAAW,EAAE,WAAW;MACxBC,KAAK,EAAE,CACH;QACIC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAIC,MAAM,CAAC,UAAU,CAAC;QAC/BjG,OAAO,EAAE;MACb,CAAC,EACD;QAAEkG,GAAG,EAAE,CAAC;QAAElG,OAAO,EAAE;MAA4B,CAAC,EAChD;QAAEmG,GAAG,EAAE,CAAC;QAAEnG,OAAO,EAAE;MAA4B,CAAC;IAExD,CAAC,EACD;MACI2F,GAAG,EAAE,OAAO;MACZC,OAAO,EAAE,CAAC;MACVQ,MAAMA,CAAA,EAAG;QACL,oBACIjH,KAAA,CAAAkH,aAAA,CAAAlH,KAAA,CAAAmH,QAAA,QACK9C,UAAU,IACXA,UAAU,GAAG+C,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,gBAC/BvH,KAAA,CAAAkH,aAAA;UAAAM,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,UACM,EAAC,GAAG,eACZ7H,KAAA,CAAAkH,aAAA,CAAC9E,SAAS;UACN0F,SAAS,EAAC,mBAAmB;UAC7BC,KAAK,EAAE,IAAK;UACZC,MAAM,EAAC,IAAI;UACXC,KAAK,EAAE5D,UAAW;UAClBoB,QAAQ,EAAEU,oBAAqB;UAAAqB,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAClC,CAAC,KAEA,CAAC,gBAEP7H,KAAA,CAAAkH,aAAA,CAAC9G,MAAM;UACHwF,IAAI,EAAC,MAAM;UACXsC,OAAO,EAAGC,CAAC,IAAK/C,gBAAgB,CAAC,CAAE;UAAAoC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAEnC7H,KAAA,CAAAkH,aAAA,CAAC9F,YAAY;UAAAoG,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC,eACZ,CAEd,CAAC;MAEX;IACJ,CAAC,CACJ;IACD,MAAMO,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACI/B,GAAG,EAAE,SAAS;QACdC,OAAO,EAAE,CAAC;QACV+B,YAAY,EAAE,YAAY;QAC1BV,SAAS,EAAE;MACf,CAAC,EACD;QACItB,GAAG,EAAE,QAAQ;QACbC,OAAO,EAAE,CAAC;QACVqB,SAAS,EAAE;MACf,CAAC,EACD;QACItB,GAAG,EAAE,IAAI;QACTC,OAAO,EAAE,CAAC;QACVb,IAAI,EAAE,QAAQ;QACdc,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,CACH;UACIC,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE,IAAIC,MAAM,CAAC,UAAU,CAAC;UAC/BjG,OAAO,EAAE;QACb,CAAC,EACD;UACIkG,GAAG,EAAE,EAAE;UACPlG,OAAO,EAAE;QACb,CAAC,EACD;UACImG,GAAG,EAAE,EAAE;UACPnG,OAAO,EAAE;QACb,CAAC;MAET,CAAC,EACD,IAAIuD,QAAQ,GAAGmC,cAAc,GAAG,EAAE,CAAC;IAE3C,CAAC;IACD,OAAO6B,IAAI;EACf,CAAC;EAEDnI,SAAS,CAAC,MAAM;IACZ,IAAI,CAACyD,oBAAoB,EAAE;MACvB,IAAI+E,gBAAgB,GAAGC,4BAA4B,CAAC,CAAC;MACrD,IAAID,gBAAgB,EAAE;QAClB9E,uBAAuB,CAAC,IAAI,CAAC;QAC7BK,QAAQ,CAAC1C,UAAU,CAACmH,gBAAgB,CAAC,CAAC;QACtC;MACJ;IACJ;IACA,IAAIvE,KAAK,KAAK,IAAI,EAAE;MAAA,IAAAyE,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAChB,IAAIhG,KAAK,aAALA,KAAK,wBAAA8F,eAAA,GAAL9F,KAAK,CAAEiC,QAAQ,cAAA6D,eAAA,wBAAAC,qBAAA,GAAfD,eAAA,CAAiBG,KAAK,cAAAF,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBG,IAAI,cAAAF,sBAAA,uBAA5BA,sBAAA,CAA8BG,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACtCzG,KAAK,CAAC0G,OAAO,CAACC,IAAI,CACd,GAAG3G,KAAK,aAALA,KAAK,wBAAAoG,gBAAA,GAALpG,KAAK,CAAEiC,QAAQ,cAAAmE,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBH,KAAK,cAAAI,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBH,IAAI,cAAAI,sBAAA,uBAA5BA,sBAAA,CAA8BpE,QAAQ,GAAGlC,KAAK,aAALA,KAAK,wBAAAuG,gBAAA,GAALvG,KAAK,CAAEiC,QAAQ,cAAAsE,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBN,KAAK,cAAAO,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBN,IAAI,cAAAO,sBAAA,uBAA5BA,sBAAA,CAA8BN,MAAM,EACpF,CAAC;MACL,CAAC,MAAM;QACHnG,KAAK,CAAC0G,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;MAC3B;IACJ;EACJ,CAAC,EAAE,CAACtF,KAAK,EAAErB,KAAK,CAAC0G,OAAO,EAAE1G,KAAK,aAALA,KAAK,wBAAAC,gBAAA,GAALD,KAAK,CAAEiC,QAAQ,cAAAhC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBgG,KAAK,cAAA/F,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBgG,IAAI,cAAA/F,sBAAA,uBAA5BA,sBAAA,CAA8BgG,MAAM,CAAC,CAAC;EAEhE,MAAMN,4BAA4B,GAAGA,CAAA,KAAM;IACvC,IAAIe,YAAY,GAAG,IAAIC,eAAe,CAACpG,MAAM,CAACwB,QAAQ,CAACkE,MAAM,CAAC;IAC9D,IAAIW,OAAO,GAAGF,YAAY,CAACG,GAAG,CAAC,SAAS,CAAC;IACzC,IAAIC,QAAQ,GAAGJ,YAAY,CAACG,GAAG,CAAC,UAAU,CAAC;IAC3C,IAAIE,uBAAuB,GAAG,IAAI;IAClC,IAAIH,OAAO,IAAIE,QAAQ,EAAE;MACrB,OAAO;QACH9D,OAAO,EAAE,oBAAoB;QAC7BgE,EAAE,EAAEJ,OAAO;QACXpE,MAAM,EAAEsE,QAAQ;QAChBC;MACJ,CAAC;IACL;EACJ,CAAC;EAED,SAAS3G,YAAYA,CAAA,EAAG;IACpB,MAAM6G,WAAW,GAAG,IAAI1C,IAAI,CAAC,CAAC;IAC9B,MAAM2C,KAAK,GAAGD,WAAW,CAACE,QAAQ,CAAC,CAAC;IACpC,IAAID,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,EAAE,EAAE;MAC1B,OAAO,SAAS;IACpB,CAAC,MAAM,IAAIA,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAE,EAAE;MAClC,OAAO,WAAW;IACtB,CAAC,MAAM,IAAIA,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAE,EAAE;MAClC,OAAO,SAAS;IACpB,CAAC,MAAM;MACH,OAAO,OAAO;IAClB;EACJ;EACAhK,SAAS,CAAC,MAAM;IACZiD,YAAY,CAACC,YAAY,CAAC,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgH,eAAe,GAAGA,CAACC,WAAW,EAAEC,aAAa,KAAK;IACpD;IACA,IAAIC,YAAY,GAAGF,WAAW,CAACG,MAAM,CAACtC,KAAK;;IAE3C;IACA,IAAIuC,gBAAgB;;IAEpB;IACA,IAAIF,YAAY,CAAC3F,MAAM,GAAG,CAAC,EAAE;MACzB;MACA;MACA6F,gBAAgB,GAAGH,aAAa,CAACI,MAAM,CAAEC,IAAI,IACzCA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,YAAY,CAACM,WAAW,CAAC,CAAC,CAC/D,CAAC;IACL;;IAEA;IACA7G,mBAAmB,CAACyG,gBAAgB,CAAC;EACzC,CAAC;EAED,MAAMM,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACHC,MAAM,EAAE,EAAE;MACVC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAA5G,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CAACC,MAAM,KAAI,CAAC,GAAG,CAAC,GAAG,CAAC;MACrDyG,EAAE,EAAE,CAAA7G,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CAACC,MAAM,KAAI,CAAC,GAAG,CAAC,GAAG,CAAC;MACrD0G,GAAG,EAAE,CAAA9G,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CAACC,MAAM,KAAI,CAAC,GAAG,CAAC,GAAG;IACzD,CAAC;EACL,CAAC;EAED,MAAM2G,YAAY,GAAG/I,OAAO,CAACC,GAAG,CAAC+I,QAAQ,IAAI,YAAY;EACzD,MAAMC,iBAAiB,GAAGjJ,OAAO,CAACC,GAAG,CAACiJ,mBAAmB,IAAI,MAAM;EACnE,OACI,CAAC/H,oBAAoB,iBACjB1D,KAAA,CAAAkH,aAAA;IACIY,SAAS,EAAE,qBAAqB7E,SAAS,qBAAsB;IAAAuE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/D7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,mBAAmB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,qBAAqB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAAC,eAC3C7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,qBAAqB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAAC,eAC3C7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,qBAAqB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAAC,eAC3C7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,qBAAqB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CACzC,CAAC,eACN7H,KAAA,CAAAkH,aAAA;IACIY,SAAS,EAAE,4CAA4C7E,SAAS,IAAI,SAAS,GAAG,eAAe,GAAG,eAAe,EAAG;IAAAuE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvH,MACO,EAAC,GAAG,eACR7H,KAAA,CAAAkH,aAAA,CAACjF,aAAa;IACVyJ,QAAQ,EAAE,CACN,MAAM,EACN,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,IAAI,CACN;IACFC,KAAK,EAAE,GAAI;IACXC,MAAM,EAAEC,QAAS;IACjB/D,SAAS,EAAC,iEAAiE;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC9E,CAAC,EAAC,GAAG,EAAC,mBAEP,CAAC,eACL7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,sCAAsC;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjD7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,2BAA2B;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtC7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,sBAAsB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,qEAAqE;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChF7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,8DAA8D;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OACrE,EAAC,GAAG,eACT7H,KAAA,CAAAkH,aAAA,CAAC3F,YAAY;IAACwI,EAAE,EAAC,qBAAqB;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACvC,CAAC,eACN7H,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,8BAA8B;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzC7H,KAAA,CAAAkH,aAAA;IACI4E,GAAG,EAAC,SAAS;IACbC,GAAG,EAAEC,OAAO,CAAC,6BAA6B,CAAE;IAC5CC,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAA1E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC/B,CAAC,EAAC,GACF,CACJ,CAAC,eACN7H,KAAA,CAAAkH,aAAA,CAAC1G,IAAI;IACD2L,gBAAgB,EAAC,OAAO;IACxBC,QAAQ,EAAEhJ,WAAW,GAAG,GAAI;IAAAoE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE5B7H,KAAA,CAAAkH,aAAA,CAAC1G,IAAI,CAAC6L,OAAO;IACT7F,GAAG,EAAC,OAAO;IACX8F,GAAG,eACCtM,KAAA,CAAAkH,aAAA;MAAAM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI7H,KAAA,CAAAkH,aAAA,CAACjG,YAAY;MAAAuG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,UACd,CACT;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAED7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI;IACDiM,aAAa,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAE;IAClC7B,IAAI,EAAC,OAAO;IACZlF,QAAQ,EAAEA,QAAS;IACnBT,cAAc,EAAEA,cAAe;IAC/B8C,SAAS,EAAC,6BAA6B;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvC7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI,CAACmM,IAAI;IACNjE,YAAY,EAAC,OAAO;IACpBV,SAAS,EAAC,WAAW;IACrB6C,IAAI,EAAC,SAAS;IAAAnD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEd7H,KAAA,CAAAkH,aAAA,CAAC3G,KAAK;IAAAiH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACD,CAAC,eACZ7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI,CAACmM,IAAI;IACNjE,YAAY,EACR8C,YAAY,GACN,EAAE,GACF,kBACT;IACD3E,KAAK,EAAE,CACH;MACIC,QAAQ,EAAE,IAAI;MACd/F,OAAO,EACH;IACR,CAAC,CACH;IACF8J,IAAI,EAAC,IAAI;IAAAnD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAET7H,KAAA,CAAAkH,aAAA,CAAC3G,KAAK;IAACmG,WAAW,EAAC,OAAO;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACrB,CAAC,eACZ7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI,CAACmM,IAAI;IACNjE,YAAY,EACR8C,YAAY,GAAG,EAAE,GAAG,UACvB;IACD3E,KAAK,EAAE,CACH;MACIC,QAAQ,EAAE,IAAI;MACd/F,OAAO,EACH;IACR,CAAC,CACH;IACF8J,IAAI,EAAC,QAAQ;IAAAnD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEb7H,KAAA,CAAAkH,aAAA,CAAC3G,KAAK;IACFqF,IAAI,EAAC,UAAU;IACfc,WAAW,EAAC,UAAU;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzB,CACM,CAAC,eACZ7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI,CAACmM,IAAI;IAAAjF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+CAGP,eAAA7H,KAAA,CAAAkH,aAAA;IACIwF,IAAI,EAAE,GAAGhK,WAAW,oBAAqB;IACzC6H,MAAM,EAAC,QAAQ;IACfoC,GAAG,EAAC,qBAAqB;IACzB7E,SAAS,EAAC,uCAAuC;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpD,gBAEE,CACI,CAAC,eACZ7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI,CAACmM,IAAI;IAAAjF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACN7H,KAAA,CAAAkH,aAAA,CAAC9G,MAAM;IACHwF,IAAI,EAAC,SAAS;IACdkC,SAAS,EAAC,SAAS;IACnB8E,QAAQ,EAAC,QAAQ;IAAApF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjB7H,KAAA,CAAAkH,aAAA,CAAC3F,YAAY;IAACwI,EAAE,EAAC,qBAAqB;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CAMD,CACT,CACI,CAAC,eACf7H,KAAA,CAAAkH,aAAA,CAAC1G,IAAI,CAAC6L,OAAO;IACT7F,GAAG,EAAC,YAAY;IAChB8F,GAAG,eACCtM,KAAA,CAAAkH,aAAA;MAAAM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI7H,KAAA,CAAAkH,aAAA,CAAChG,cAAc;MAAAsG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eAChB,CACT;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAED7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI;IACDwH,SAAS,EAAC,6BAA6B;IACvC+E,MAAM,EAAC,UAAU;IACjBpH,QAAQ,EAAEA,QAAS;IACnBT,cAAc,EAAEA,cAAe;IAC/B8H,GAAG,EAAE7I,kBAAmB;IAAAuD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExB7H,KAAA,CAAAkH,aAAA,CAACzF,WAAW;IACR2G,IAAI,EAAE9B,mBAAmB,CAAC,CAAE;IAC5ByG,IAAI,EAAE9I,kBAAmB;IAAAuD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC5B,CAAC,eACF7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI,CAACmM,IAAI;IAAAjF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+CAGP,eAAA7H,KAAA,CAAAkH,aAAA;IACIwF,IAAI,EAAE,GAAGhK,WAAW,oBAAqB;IACzC6H,MAAM,EAAC,QAAQ;IACfoC,GAAG,EAAC,qBAAqB;IACzB7E,SAAS,EAAC,uCAAuC;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpD,gBAEE,CACI,CAAC,eACZ7H,KAAA,CAAAkH,aAAA,CAAC5G,IAAI,CAACmM,IAAI;IAAAjF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACN7H,KAAA,CAAAkH,aAAA,CAAC9G,MAAM;IACHwF,IAAI,EAAC,SAAS;IACdgH,QAAQ,EAAC,QAAQ;IACjB9E,SAAS,EAAC,SAAS;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB,SAEO,CACD,CACT,CACI,CACZ,CAAC,EACN,CAACjG,YAAY,CAAC,CAAC,iBACZ5B,KAAA,CAAAkH,aAAA;IAAKY,SAAS,EAAC,gBAAgB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3B7H,KAAA,CAAAkH,aAAA;IAAIY,SAAS,EAAC,iBAAiB;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,EACjC2D,iBAAiB,gBACdxL,KAAA,CAAAkH,aAAA;IACIwF,IAAI,EAAEpK,qBAAsB;IAC5B0K,QAAQ,EAAC,MAAM;IACflF,SAAS,EAAC,6BAA6B;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAMvC7H,KAAA,CAAAkH,aAAA;IAAMY,SAAS,EAAC,gGAAgG;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5G7H,KAAA,CAAAkH,aAAA;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI7H,KAAA,CAAAkH,aAAA,CAAC/E,iBAAiB;IAAC2F,SAAS,EAAC,2BAA2B;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACxD,CAAC,wBAEL,CACP,CAAC,gBAEJ7H,KAAA,CAAAkH,aAAA;IACIwF,IAAI,EAAEpK,qBAAsB;IAC5B0K,QAAQ,EAAC,MAAM;IAAAxF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEf7H,KAAA,CAAAkH,aAAA;IACI6E,GAAG,EAAEhK,YAAa;IAClBkL,KAAK,EAAE,EAAG;IAAAzF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACb,CAAC,gBAEH,CAEN,CACR,eACD7H,KAAA,CAAAkH,aAAA;IAAGY,SAAS,EAAC,6DAA6D;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAExE,CAAC,EACHvD,aAAa,iBACVtE,KAAA,CAAAkH,aAAA,CAACxG,KAAK;IACFqH,KAAK,eACD/H,KAAA,CAAAkH,aAAA;MAAKY,SAAS,EAAC,mEAAmE;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC7E,CAAAtD,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CACzBC,MAAM,KAAI,CAAC,iBACZ3E,KAAA,CAAAkH,aAAA;MAAKY,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEpB,CACR,EACA,CAAAtD,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CACzBC,MAAM,IAAG,CAAC,iBACX3E,KAAA,CAAAkH,aAAA,CAAC3G,KAAK;MACFuM,GAAG,EAAEtI,kBAAmB;MACxBsD,SAAS,EAAC,6BAA6B;MACvCpB,WAAW,EAAC,uBAAuB;MACnCwG,QAAQ,EAAG/E,CAAC,IACRgC,eAAe,CACXhC,CAAC,EACD5D,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CACZE,IAAI,CAACC,IACf,CACH;MACDyI,UAAU;MAAA3F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACb,CAEJ,CACR;IACDuF,OAAO,EAAE9I,aAAc;IACvB+I,QAAQ,EAAE3H,YAAa;IACvBuH,KAAK,EACD,CAAA1I,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CAACC,MAAM,KACpC,CAAC,GACK,GAAG,GACH,IACT;IACD2I,MAAM,EAAE,IAAK;IACblB,QAAQ,EAAE,IAAK;IAAA5E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEf7H,KAAA,CAAAkH,aAAA;IACIY,SAAS,EAAE1B,qBAAqB,CAC5B7B,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,IAAI,CAACC,IAAI,CAACC,MAClC,CAAE;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF7H,KAAA,CAAAkH,aAAA,CAACvG,IAAI;IACD4M,IAAI,EAAEzC,oBAAoB,CAAC;IAC3B;IAAA;IACAhD,SAAS,EAAC,SAAS;IACnB0F,UAAU,EACN1J,gBAAgB,GACVA,gBAAgB,GAChBS,kBAAkB,CAACE,IAAI,CAClBC,IACd;IACD+I,UAAU,EAAG/C,IAAI,iBACb1K,KAAA,CAAAkH,aAAA,CAACvG,IAAI,CAAC8L,IAAI;MACNjG,GAAG,EAAEkE,IAAI,CAACX,EAAG;MACb7B,OAAO,EAAGC,CAAC,IACPtC,eAAe,CACX6E,IAAI,CAAC5E,MACT,CACH;MACDgC,SAAS,EAAC,qCAAqC;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAE/C7H,KAAA,CAAAkH,aAAA,CAACvG,IAAI,CAAC8L,IAAI,CAACiB,IAAI;MACX3F,KAAK,eACD/H,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,sBAAsB;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAChC6C,IAAI,CAACiD,WAAW,iBACb3N,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,EAAE;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACb7H,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,6IAA6I;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACxJ7H,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,0BAA0B;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAM,CAAC,EAAC,GAAG,EAEhD6C,IAAI,CAACiD,WAAW,EAClB,GACD,CACJ,CACR,eACD3N,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,+DAA+D;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC1E7H,KAAA,CAAAkH,aAAA;QAAAM,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACI7H,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,2CAA2C;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACtD7H,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,kBAAkB;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC7B7H,KAAA,CAAAkH,aAAA;QACI6E,GAAG,EACCrB,IAAI,CAACkD,IACR;QACD9F,SAAS,EAAC,qCAAqC;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAClD,CACA,CACJ,CACJ,CAAC,eACN7H,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,yBAAyB;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACpC7H,KAAA,CAAAkH,aAAA;QAAIY,SAAS,EAAC,iCAAiC;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAC1C,GAAG,EAEA6C,IAAI,CAACC,IAAI,EACX,GACF,CAAC,eACL3K,KAAA,CAAAkH,aAAA;QAAOY,SAAS,EAAC,yBAAyB;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAElC6C,IAAI,CAACmD,UAAU,EACjB,GACC,CACN,CAAC,eACN7N,KAAA,CAAAkH,aAAA;QAAKY,SAAS,EAAC,yBAAyB;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACpC7H,KAAA,CAAAkH,aAAA;QAAIY,SAAS,EAAC,iCAAiC;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC3C7H,KAAA,CAAAkH,aAAA,CAAC7E,IAAI;QACDyF,SAAS,EACLlE,QAAQ,GACF,wBAAwB,GACxB4B,SACT;QACD5B,QAAQ,EACJA,QAAQ,GACF;UACIkK,OAAO,EACHpD,IAAI,CAACC;QACb,CAAC,GACD,KACT;QAAAnD,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAGG6C,IAAI,CAACC,IAEP,CAAC,EAAC,GACR,CAAC,EAAC,GAAG,eACT3K,KAAA,CAAAkH,aAAA;QAAOY,SAAS,EAAC,yBAAyB;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACtC7H,KAAA,CAAAkH,aAAA,CAAC7E,IAAI;QACDyF,SAAS,EACLlE,QAAQ,GACF,wBAAwB,GACxB,IACT;QACDA,QAAQ,EACJA,QAAQ,GACF;UACIkK,OAAO,EACHpD,IAAI,CAACmD;QACb,CAAC,GACD,KACT;QAAArG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAGG6C,IAAI,CAACmD,UAEP,CACH,CACN,CACJ,CACJ,CACR;MAAArG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACJ,CACM,CACb;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CACF,CAEV,CAAC,eACN7H,KAAA,CAAAkH,aAAA,CAAC1F,QAAQ;IAAAgG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACV,CACJ,CACJ,CACR;AAET,CAAC;AAED,eAAejF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}