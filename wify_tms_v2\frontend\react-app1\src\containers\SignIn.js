import React, { useEffect, useRef, useState } from 'react';
import {
    Button,
    Checkbox,
    Form,
    Input,
    Tabs,
    Statistic,
    Modal,
    List,
    Avatar,
    message,
    Typography,
} from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import {
    MailOutlined,
    MobileOutlined,
    PhoneOutlined,
    SyncOutlined,
} from '@ant-design/icons';
import { superAdminSignIn, userSignIn } from '../appRedux/actions/Auth';
import IntlMessages from 'util/IntlMessages';
import InfoView from 'components/InfoView';
import FormBuilder from 'antd-form-builder';
import { number } from 'prop-types';
import { SELECT_ORG_FOR_LOGIN } from '../constants/ActionTypes';
import { isAndroidApp, isMobileView, setHtmlHeadTitle } from '../util/helpers';
import android_icon from '../assets/images/android_icon.png';
import android_play_icon from '../assets/images/google_play_icon.svg';
import { TypeAnimation } from 'react-type-animation';
import LaborDay from '../components/Aesthetics/LaborDay';
import { HiOutlineDownload } from 'react-icons/hi';

const { Countdown } = Statistic;
const { Text } = Typography;

const tms_apk_download_link = process.env.REACT_APP_TMS_APK_LINK_TO_DOWNLOAD;
const website_url =
    process.env.REACT_APP_WEBSITE_URL || 'https://home.wify.co.in';

const SignIn = (props) => {
    // const {render_helper} = useState()
    const [timeOfDay, setTimeOfDay] = useState(getTimeOfDay());
    const [screenWidth, setScreenWidth] = useState(window.innerWidth);
    const [renderHelper, setRenderHelper] = useState(false);
    const [superAdminAccessFlag, setsuperAdminAccessFlag] = useState(false);
    const [ellipsis, setEllipsis] = useState(true);
    const [filteredOrgArray, setFilteredOrgArray] = useState(null);
    const dispatch = useDispatch();
    const mobileLoginFormRef = useRef();
    const token = useSelector(({ auth }) => auth.token);

    const otp_sent = useSelector(({ auth }) => auth.otp_sent);
    const otp_expiry = useSelector(({ auth }) => auth.otp_expiry);

    const is_multi_orgs = useSelector(({ auth }) => auth.is_multi_orgs);
    const multi_orgs_payload = useSelector(
        ({ auth }) => auth.multi_orgs_payload
    );

    const orgsSearchInputRef = useRef(null);

    useEffect(() => {
        if (is_multi_orgs && multi_orgs_payload?.resp.data.length > 3) {
            orgsSearchInputRef.current.focus();
        }

        setHtmlHeadTitle(props.location.pathname);
    }, [is_multi_orgs]);

    // console.log("multi_orgs_payload",multi_orgs_payload);
    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    const onResendOTPClick = () => {
        let form_data = mobileLoginFormRef.current.getFieldsValue();
        form_data.id_key = undefined;
        onFinish(form_data);
    };

    const handleCancel = () => {
        let payload = {
            ...multi_orgs_payload,
            resp: undefined,
        };
        setFilteredOrgArray(null);
        dispatch({ type: SELECT_ORG_FOR_LOGIN, payload: payload });
    };

    const handleOrgSelect = (org_id) => {
        if (multi_orgs_payload.form_data.id_type == 'MOBILE_NUM') {
            mobileLoginFormRef.current.setFieldsValue({
                org_id: org_id,
            });
        }
        // message.success('seelcted org ' + org_id);
        let newFormData = {
            ...multi_orgs_payload.form_data,
            org_id: org_id,
        };
        onFinish(newFormData);
        dispatch({ type: SELECT_ORG_FOR_LOGIN, payload: undefined });
    };

    const onFinish = (values) => {
        // dispatch({type:FETCH_START});
        setFilteredOrgArray(null);
        dispatch(userSignIn(values));
    };

    const onOTPCountDownFinish = () => {
        setRenderHelper(!renderHelper);
    };

    // Function to determine the CSS class for the brand container based on the number of organizations.
    const brandContainerByCount = (orgListLength) => {
        // Check if the number of organizations is greater than 9.
        if (orgListLength > 9) {
            // If more than 9, apply custom scroll bar and list container styling.
            return 'wy-scroll-bar-custom wy-brands-list-container';
        }
        // Check if the number of organizations is exactly 9.
        else if (orgListLength == 9) {
            // If exactly 9, apply a specific styling for the count of nine.
            return 'wy-brands-list-count-nine';
        }
        // Check if the number of organizations is less than 9 but greater than 6.
        else if (orgListLength < 9 && orgListLength > 6) {
            // If between 7 and 9, apply a specific styling for counts from six to nine.
            return 'wy-brands-list-count-six-to-nine';
        }
        // Check if the number of organizations is less than 6 but greater than 3.
        else if (orgListLength < 6 && orgListLength > 3) {
            // If between 4 and 6, apply a specific styling for counts from three to six.
            return 'wy-brands-list-count-three-to-six';
        }
        // Check if the number of organizations is 3 or fewer.
        else if (orgListLength <= 3) {
            // If 3 or fewer, apply a specific styling for counts up to three.
            return 'wy-brands-list-count-three';
        }
        // In case none of the above conditions are met, return an empty string.
        else {
            return '';
        }
    };

    const getMobilNumFormMeta = () => {
        const enter_otp_meta = [
            {
                key: 'id_key',
                type: 'number',
                colSpan: 1,
                placeholder: 'Enter OTP',
                rules: [
                    {
                        required: true,
                        pattern: new RegExp('^[0-9]*$'),
                        message: 'Enter your right OTP',
                    },
                    { min: 4, message: 'OTP must be min 4 digits.' },
                    { max: 4, message: 'OTP must be max 4 digits.' },
                ],
            },
            {
                key: 'retry',
                colSpan: 1,
                render() {
                    return (
                        <>
                            {otp_expiry &&
                            otp_expiry > Math.floor(Date.now()) ? (
                                <span>
                                    Retry in{' '}
                                    <Countdown
                                        className="gx-d-inline-block"
                                        title={null}
                                        format="ss"
                                        value={otp_expiry}
                                        onFinish={onOTPCountDownFinish}
                                    />
                                    s
                                </span>
                            ) : (
                                <Button
                                    type="link"
                                    onClick={(e) => onResendOTPClick()}
                                >
                                    <SyncOutlined /> Resend OTP
                                </Button>
                            )}
                        </>
                    );
                },
            },
        ];
        const meta = {
            columns: 2,
            formItemLayout: null,
            fields: [
                {
                    key: 'id_type',
                    colSpan: 1,
                    initialValue: 'MOBILE_NUM',
                    className: 'gx-d-none',
                },
                {
                    key: 'org_id',
                    colSpan: 1,
                    className: 'gx-d-none',
                },
                {
                    key: 'id',
                    colSpan: 2,
                    type: 'number',
                    placeholder: 'Mobile no.',
                    rules: [
                        {
                            required: true,
                            pattern: new RegExp('^[0-9]*$'),
                            message: 'Please enter your right mobile number!',
                        },
                        {
                            min: 10,
                            message: 'Mobile no must be min 10 digits.',
                        },
                        {
                            max: 10,
                            message: 'Mobile no must be max 10 digits.',
                        },
                    ],
                },
                ...(otp_sent ? enter_otp_meta : ''),
            ],
        };
        return meta;
    };

    useEffect(() => {
        if (!superAdminAccessFlag) {
            let superAdminParams = decodeParamsFrSuprAdminLogin();
            if (superAdminParams) {
                setsuperAdminAccessFlag(true);
                dispatch(userSignIn(superAdminParams));
                return;
            }
        }
        if (token !== null) {
            if (props?.location?.state?.from?.search) {
                props.history.push(
                    `${props?.location?.state?.from?.pathname}${props?.location?.state?.from?.search}`
                );
            } else {
                props.history.push('/');
            }
        }
    }, [token, props.history, props?.location?.state?.from?.search]);

    const decodeParamsFrSuprAdminLogin = () => {
        let searchParams = new URLSearchParams(window.location.search);
        let user_id = searchParams.get('user_id');
        let my_token = searchParams.get('my_token');
        let is_check_technician_app = true;
        if (user_id && my_token) {
            return {
                id_type: 'SUPER_ADMIN_ACCESS',
                id: user_id,
                id_key: my_token,
                is_check_technician_app,
            };
        }
    };

    function getTimeOfDay() {
        const currentTime = new Date();
        const hours = currentTime.getHours();
        if (hours >= 5 && hours < 12) {
            return 'morning';
        } else if (hours >= 12 && hours < 16) {
            return 'afternoon';
        } else if (hours >= 16 && hours < 19) {
            return 'evening';
        } else {
            return 'night';
        }
    }
    useEffect(() => {
        setTimeOfDay(getTimeOfDay());
    }, []);

    // Function to filter a list of organizations based on a search event's input.
    const filterMultiOrgs = (searchEvent, multiOrgsData) => {
        // Retrieve the search string entered by the user from the event target's value.
        let searchString = searchEvent.target.value;

        // Declare a variable to hold the filtered list of organizations.
        let filteredOrgsList;

        // Check if the search string has a length greater than 0, meaning the user has entered some text.
        if (searchString.length > 0) {
            // Filter the list of organizations by checking if the organization's name includes the search string.
            // Convert both the organization's name and the search string to lowercase to make the search case-insensitive.
            filteredOrgsList = multiOrgsData.filter((item) =>
                item.name.toLowerCase().includes(searchString.toLowerCase())
            );
        }

        // Update the state that stores the filtered list of organizations.
        setFilteredOrgArray(filteredOrgsList);
    };

    const getGridViewCountData = () => {
        return {
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 3,
            lg: multi_orgs_payload?.resp.data.length == 2 ? 2 : 3,
            xl: multi_orgs_payload?.resp.data.length == 2 ? 2 : 3,
            xxl: multi_orgs_payload?.resp.data.length == 2 ? 2 : 3,
        };
    };

    const isProduction = process.env.NODE_ENV == 'production';
    const showPlayStoreIcon = process.env.REACT_APP_PLAYSTORE == 'true';
    return (
        !superAdminAccessFlag && (
            <div
                className={`gx-app-login-wrap ${timeOfDay} gx-overflow-hidden`}
            >
                <div className="animation-wrapper">
                    <div className="particle particle-1"></div>
                    <div className="particle particle-2"></div>
                    <div className="particle particle-3"></div>
                    <div className="particle particle-4"></div>
                </div>
                <h3
                    className={` gx-mt-3 custom-text blend gx-z-index-20 ${timeOfDay == 'morning' ? 'gx-text-black' : 'gx-text-white'}`}
                >
                    Your{' '}
                    <TypeAnimation
                        sequence={[
                            'Time',
                            3000,
                            'Task',
                            3000,
                            'Ticket',
                            3000,
                            'Team',
                            3000,
                        ]}
                        speed={250}
                        repeat={Infinity}
                        className="gx-text-uppercase gx-text-underline custom-text gx-fs-2xl blend"
                    />{' '}
                    Management System
                </h3>
                <div className="gx-app-login-container gx-z-index-20">
                    <div className="gx-app-login-main-content">
                        <div className="gx-app-login-content">
                            <div className="gx-w-100 gx-d-flex gx-align-items-center gx-justify-content-between">
                                <div className="gx-fs-xxl gx-text-center wy-typo-color gx-font-weight-medium">
                                    TMS -{' '}
                                    <IntlMessages id="app.userAuth.signIn" />
                                </div>
                                <div className="gx-mt-2 gx-position-relative">
                                    <img
                                        alt="example"
                                        src={require('assets/images/wify_logo.png')}
                                        style={{ maxWidth: '94px' }}
                                    />{' '}
                                </div>
                            </div>
                            <Tabs
                                defaultActiveKey="email"
                                centered={screenWidth < 767}
                            >
                                <Tabs.TabPane
                                    key="email"
                                    tab={
                                        <span>
                                            <MailOutlined /> EMAIL
                                        </span>
                                    }
                                >
                                    <Form
                                        initialValues={{ remember: true }}
                                        name="basic"
                                        onFinish={onFinish}
                                        onFinishFailed={onFinishFailed}
                                        className="gx-signin-form gx-form-row0"
                                    >
                                        <Form.Item
                                            initialValue="EMAIL"
                                            className="gx-d-none"
                                            name="id_type"
                                        >
                                            <Input />
                                        </Form.Item>
                                        <Form.Item
                                            initialValue={
                                                isProduction
                                                    ? ''
                                                    : '<EMAIL>'
                                            }
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'The input is not valid E-mail!',
                                                },
                                            ]}
                                            name="id"
                                        >
                                            <Input placeholder="Email" />
                                        </Form.Item>
                                        <Form.Item
                                            initialValue={
                                                isProduction ? '' : 'demo#123'
                                            }
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please input your Password!',
                                                },
                                            ]}
                                            name="id_key"
                                        >
                                            <Input
                                                type="password"
                                                placeholder="Password"
                                            />
                                        </Form.Item>
                                        <Form.Item>
                                            By signing up to this app, you agree
                                            with the
                                            <a
                                                href={`${website_url}/tms/privacypolicy`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="gx-signup-form-forgot gx-link gx-ml-2"
                                            >
                                                Privacy Policy
                                            </a>
                                        </Form.Item>
                                        <Form.Item>
                                            <Button
                                                type="primary"
                                                className="gx-mb-0"
                                                htmlType="submit"
                                            >
                                                <IntlMessages id="app.userAuth.signIn" />
                                            </Button>
                                            {/* <span><IntlMessages id="app.userAuth.or"/></span> 
                {/* <span><IntlMessages id="app.userAuth.or"/></span> 
                    {/* <span><IntlMessages id="app.userAuth.or"/></span> 
                    <Link to="/signup"><IntlMessages
                    id="app.userAuth.signUp"/></Link> */}
                                        </Form.Item>
                                    </Form>
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    key="mobile_num"
                                    tab={
                                        <span>
                                            <MobileOutlined /> MOBILE NO.
                                        </span>
                                    }
                                >
                                    <Form
                                        className="gx-signin-form gx-form-row0"
                                        layout="vertical"
                                        onFinish={onFinish}
                                        onFinishFailed={onFinishFailed}
                                        ref={mobileLoginFormRef}
                                    >
                                        <FormBuilder
                                            meta={getMobilNumFormMeta()}
                                            form={mobileLoginFormRef}
                                        />
                                        <Form.Item>
                                            By signing up to this app, you agree
                                            with the
                                            <a
                                                href={`${website_url}/tms/privacypolicy`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="gx-signup-form-forgot gx-link gx-ml-2"
                                            >
                                                Privacy Policy
                                            </a>
                                        </Form.Item>
                                        <Form.Item>
                                            <Button
                                                type="primary"
                                                htmlType="submit"
                                                className="gx-mb-0"
                                            >
                                                Sign In
                                            </Button>
                                        </Form.Item>
                                    </Form>
                                </Tabs.TabPane>
                            </Tabs>
                            {!isAndroidApp() && (
                                <div className="gx-text-center">
                                    <hr className="gx-mt-1 gx-mb-4" />
                                    {showPlayStoreIcon ? (
                                        <a
                                            href={tms_apk_download_link}
                                            download="wify"
                                            className="wy-temp-apk-download-button"
                                        >
                                            {/* <img
                                                src={android_play_icon}
                                                width={150}
                                            /> */}
                                            <span className="wy-temp-apk-download-section-wrapper gx-d-flex gx-align-items-center gx-justify-content-center">
                                                <span>
                                                    <HiOutlineDownload className="wy-temp-apk-download-icon" />
                                                </span>
                                                Download Android APK
                                            </span>
                                        </a>
                                    ) : (
                                        <a
                                            href={tms_apk_download_link}
                                            download="wify"
                                        >
                                            <img
                                                src={android_icon}
                                                width={50}
                                            />
                                            Download app
                                        </a>
                                    )}
                                </div>
                            )}
                            <p className="gx-text-center gx-text-black gx-fs-md gx-mt-4  gx-text-bold">
                                Copyright © Wify Technology
                            </p>
                            {is_multi_orgs && (
                                <Modal
                                    title={
                                        <div className="wy-flex-responsive gx-align-items-start gx-align-items-md-center ">
                                            {multi_orgs_payload?.resp.data
                                                .length <= 3 && (
                                                <div className="gx-mr-2">
                                                    Choose one..
                                                </div>
                                            )}
                                            {multi_orgs_payload?.resp.data
                                                .length > 3 && (
                                                <Input
                                                    ref={orgsSearchInputRef}
                                                    className="wy-login-search-brand-input"
                                                    placeholder="Search by org name..."
                                                    onChange={(e) =>
                                                        filterMultiOrgs(
                                                            e,
                                                            multi_orgs_payload
                                                                ?.resp.data
                                                        )
                                                    }
                                                    allowClear
                                                />
                                            )}
                                        </div>
                                    }
                                    visible={is_multi_orgs}
                                    onCancel={handleCancel}
                                    width={
                                        multi_orgs_payload?.resp.data.length ==
                                        2
                                            ? 600
                                            : 1000
                                    }
                                    footer={null}
                                    centered={true}
                                >
                                    <div
                                        className={brandContainerByCount(
                                            multi_orgs_payload?.resp.data.length
                                        )}
                                    >
                                        <List
                                            grid={getGridViewCountData()}
                                            // itemLayout="horizontal"
                                            className="gx-mr-2"
                                            dataSource={
                                                filteredOrgArray
                                                    ? filteredOrgArray
                                                    : multi_orgs_payload.resp
                                                          .data
                                            }
                                            renderItem={(item) => (
                                                <List.Item
                                                    key={item.id}
                                                    onClick={(e) =>
                                                        handleOrgSelect(
                                                            item.org_id
                                                        )
                                                    }
                                                    className="gx-task-list-item wy-brands-wrapper"
                                                >
                                                    <List.Item.Meta
                                                        title={
                                                            <div className="gx-position-relative">
                                                                {item.designation && (
                                                                    <div className="">
                                                                        <div className="wy-user-designation gx-mb-2 gx-fs-11 wy-brands-animated-text wy-brands-designation gx-d-flex gx-align-items-center gx-justify-content-start">
                                                                            <div className="icon icon-user-o gx-mr-1"></div>{' '}
                                                                            {
                                                                                item.designation
                                                                            }{' '}
                                                                        </div>
                                                                    </div>
                                                                )}
                                                                <div className="wy-gap-0 wy-flex-responsive-reverse wy-brands-mobile-padding ">
                                                                    <div>
                                                                        <div className="wy-brands-logo-wrapper wy-gradient-border">
                                                                            <div className="wy-inner-content">
                                                                                <img
                                                                                    src={
                                                                                        item.icon
                                                                                    }
                                                                                    className="wy-object-contain  wy-center-to-div"
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div className="gx-d-block gx-d-md-none">
                                                                        <h4 className="gx-mb-0 wy-brands-animated-text">
                                                                            {' '}
                                                                            {
                                                                                item.name
                                                                            }{' '}
                                                                        </h4>
                                                                        <small className="wy-brands-animated-text">
                                                                            {
                                                                                item.name_legal
                                                                            }{' '}
                                                                        </small>
                                                                    </div>
                                                                    <div className="gx-d-none gx-d-md-block">
                                                                        <h4 className="gx-mb-0 wy-brands-animated-text">
                                                                            <Text
                                                                                className={
                                                                                    ellipsis
                                                                                        ? 'wy-brand-text-ellipsis'
                                                                                        : undefined
                                                                                }
                                                                                ellipsis={
                                                                                    ellipsis
                                                                                        ? {
                                                                                              tooltip:
                                                                                                  item.name,
                                                                                          }
                                                                                        : false
                                                                                }
                                                                            >
                                                                                {
                                                                                    item.name
                                                                                }
                                                                            </Text>{' '}
                                                                        </h4>{' '}
                                                                        <small className="wy-brands-animated-text">
                                                                            <Text
                                                                                className={
                                                                                    ellipsis
                                                                                        ? 'wy-brand-text-ellipsis'
                                                                                        : null
                                                                                }
                                                                                ellipsis={
                                                                                    ellipsis
                                                                                        ? {
                                                                                              tooltip:
                                                                                                  item.name_legal,
                                                                                          }
                                                                                        : false
                                                                                }
                                                                            >
                                                                                {
                                                                                    item.name_legal
                                                                                }
                                                                            </Text>
                                                                        </small>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }
                                                    />
                                                </List.Item>
                                            )}
                                        />
                                    </div>
                                </Modal>
                            )}
                        </div>
                        <InfoView />
                    </div>
                </div>
            </div>
        )
    );
};

export default SignIn;
